# SocialSparkGen

AI-powered social media content generation platform.

## Features

- SQLite Database Integration (no external database needed)
- Simple Authentication System
- User Profile Management
- Post Generation Limits by Plan
- Pricing Tiers
- Google Gemini AI Integration

## Setup

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```

3. Create a `.env` file based on `.env.example` with your settings:
   ```
   # Backend server and API keys
   PORT=5001
   GOOGLE_API_KEY=your_google_api_key

   # JWT Secret for simple authentication (replace with a strong random string)
   JWT_SECRET=your_jwt_secret_key_here
   ```

4. Run the development server:
   ```
   npm run dev:all
   ```
   This will start both the frontend and backend servers.

## Database

The application uses SQLite for data storage, which requires no setup or external database server. The SQLite database file is automatically created in the `data/` directory when the application starts.

### Database Schema

#### Profiles Table

```sql
CREATE TABLE profiles (
  id TEXT PRIMARY KEY,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  full_name TEXT,
  plan TEXT DEFAULT 'free' CHECK(plan IN ('free', 'basic', 'pro', 'ultra')),
  posts_count INTEGER DEFAULT 0,
  posts_limit INTEGER DEFAULT 5
);
```

#### Posts Table

```sql
CREATE TABLE posts (
  id TEXT PRIMARY KEY,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  user_id TEXT NOT NULL,
  content TEXT NOT NULL,
  caption TEXT,
  image_url TEXT,
  published INTEGER DEFAULT 0,
  FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE
);
```

#### Users Table

```sql
CREATE TABLE users (
  id TEXT PRIMARY KEY,
  email TEXT NOT NULL UNIQUE,
  password_hash TEXT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (id) REFERENCES profiles(id) ON DELETE CASCADE
);
```

## Environment Variables

- `PORT`: Backend server port (default: 5001)
- `GOOGLE_API_KEY`: Google Gemini API key
- `JWT_SECRET`: Secret key for JWT token generation