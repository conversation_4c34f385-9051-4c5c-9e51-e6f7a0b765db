import { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import {
  ArrowLeft, Download, Heart, MessageCircle, Bookmark, Share2,
  Instagram, Linkedin, Facebook, MoreHorizontal, Repeat2,
  ThumbsUp, MessageSquare, Send, Eye, UserPlus
} from "lucide-react";
import XIcon from "@/components/icons/XIcon";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { AspectRatio } from "@/components/ui/aspect-ratio";

interface PostData {
  platform: string;
  caption: string;
  hashtags: string;
  imageData: string;
  imageSize: string;
}

export default function Preview() {
  const location = useLocation();
  const navigate = useNavigate();
  const [actualAspectRatio, setActualAspectRatio] = useState<number>(1);
  const [postData, setPostData] = useState<PostData | null>(null);

  useEffect(() => {
    // Get the preview ID from the URL query parameters
    const params = new URLSearchParams(location.search);
    const previewId = params.get("id");

    console.log("Preview ID received:", previewId || "None");

    if (previewId) {
      try {
        // Get the data from localStorage
        const storedData = localStorage.getItem(previewId);

        if (!storedData) {
          console.error("No data found in localStorage for ID:", previewId);
          return;
        }

        console.log("Retrieved data from localStorage");

        // Parse the JSON data
        const parsedData = JSON.parse(storedData);
        console.log("Parsed data successfully");

        // Validate the required fields
        if (!parsedData.platform || !parsedData.imageData || !parsedData.imageSize) {
          console.error("Missing required fields in preview data");
          return;
        }

        // Ensure caption and hashtags exist (even if empty strings)
        parsedData.caption = parsedData.caption || '';
        parsedData.hashtags = parsedData.hashtags || '';

        // Log the data for debugging
        console.log('Setting post data:', {
          platform: parsedData.platform,
          captionLength: parsedData.caption ? parsedData.caption.length : 0,
          hashtagsLength: parsedData.hashtags ? parsedData.hashtags.length : 0,
          hasImageData: !!parsedData.imageData,
          imageSize: parsedData.imageSize
        });

        // Set the post data
        setPostData(parsedData);

        // Clean up localStorage after use
        // We'll do this when the component unmounts
      } catch (error) {
        console.error("Error processing preview data:", error);
      }
    } else {
      // Check for the old URL parameter method for backward compatibility
      const encodedData = params.get("data");

      if (encodedData) {
        try {
          // Decode the URL-encoded string
          const decodedData = decodeURIComponent(encodedData);

          // Parse the JSON data
          const parsedData = JSON.parse(decodedData);

          // Validate the required fields
          if (!parsedData.platform || !parsedData.imageData || !parsedData.imageSize) {
            console.error("Missing required fields in preview data");
            return;
          }

          // Ensure caption and hashtags exist (even if empty strings)
          parsedData.caption = parsedData.caption || '';
          parsedData.hashtags = parsedData.hashtags || '';

          // Set the post data
          setPostData(parsedData);
        } catch (error) {
          console.error("Error processing preview data from URL:", error);
        }
      } else {
        console.error("No preview ID or data parameter found in URL");
      }
    }

    // Clean up function to remove data from localStorage when component unmounts
    return () => {
      if (previewId) {
        localStorage.removeItem(previewId);
        console.log("Cleaned up localStorage for ID:", previewId);
      }
    };
  }, [location]);

  // Calculate actual aspect ratio from loaded image
  useEffect(() => {
    if (postData?.imageData) {
      const img = new Image();
      img.onload = () => {
        const ratio = img.width / img.height;
        setActualAspectRatio(ratio);
      };
      img.src = postData.imageData;
    }
  }, [postData?.imageData]);

  const handleDownload = () => {
    if (!postData?.imageData) return;

    // Create a temporary anchor element
    const link = document.createElement('a');
    link.href = postData.imageData;
    link.download = `social-spark-${postData.platform.toLowerCase()}-${new Date().getTime()}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleBack = () => {
    navigate(-1); // Go back to the previous page
  };

  if (!postData) {
    return (
      <div className="container max-w-6xl py-8 px-4">
        <div className="flex flex-col items-center justify-center min-h-[50vh]">
          <h1 className="text-2xl font-bold mb-4">No preview data available</h1>
          <p className="text-muted-foreground mb-6 max-w-md text-center">
            Please generate content and an image first, then click the Preview button to see how your post will look.
          </p>
          <Button onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-2" /> Go Back to Editor
          </Button>
        </div>
      </div>
    );
  }

  // Calculate aspect ratio - use actual image ratio if available, otherwise default to 1:1
  const getAspectRatio = () => {
    // If imageSize is "Auto" or we have actual image dimensions, use actual ratio
    if (postData?.imageSize === "Auto" || actualAspectRatio !== 1) {
      return actualAspectRatio;
    }

    // Otherwise try to parse the imageSize string
    try {
      const [width, height] = postData.imageSize.split(':').map(Number);
      return width / height;
    } catch {
      return 1; // Default fallback
    }
  };

  // Render different platform previews
  const renderPlatformPreview = () => {
    switch (postData.platform.toLowerCase()) {
      case 'instagram':
        return renderInstagramPreview();
      case 'twitter':
      case 'x':
        return renderTwitterPreview();
      case 'linkedin':
        return renderLinkedinPreview();
      case 'facebook':
        return renderFacebookPreview();
      case 'tiktok':
        return renderTikTokPreview();
      case 'youtube':
        return renderYouTubePreview();
      default:
        return renderGenericPreview();
    }
  };

  // Instagram-style preview
  const renderInstagramPreview = () => (
    <div className="max-w-md mx-auto bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden shadow-lg">
      {/* Header */}
      <div className="flex items-center p-3 bg-white dark:bg-gray-900">
        <Avatar className="h-8 w-8 mr-3">
          <AvatarImage src="/placeholder-avatar.jpg" />
          <AvatarFallback className="bg-gradient-to-br from-purple-500 to-pink-500 text-white text-xs font-bold">SP</AvatarFallback>
        </Avatar>
        <div className="flex-1">
          <div className="flex items-center">
            <p className="text-sm font-semibold text-gray-900 dark:text-white">socialsparkapp</p>
            <div className="w-1 h-1 bg-gray-400 rounded-full mx-1"></div>
            <button className="text-xs font-semibold text-blue-500 hover:text-blue-600">Follow</button>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">SocialSpark AI</p>
        </div>
        <MoreHorizontal className="h-5 w-5 text-gray-600 dark:text-gray-400" />
      </div>

      {/* Image */}
      <AspectRatio ratio={getAspectRatio()}>
        <img
          src={postData.imageData}
          alt="Instagram post"
          className="w-full h-full object-cover"
        />
      </AspectRatio>

      {/* Actions */}
      <div className="p-3 bg-white dark:bg-gray-900">
        <div className="flex justify-between items-center mb-3">
          <div className="flex space-x-4">
            <Heart className="h-6 w-6 text-gray-900 dark:text-white hover:text-gray-600 dark:hover:text-gray-300 cursor-pointer" />
            <MessageCircle className="h-6 w-6 text-gray-900 dark:text-white hover:text-gray-600 dark:hover:text-gray-300 cursor-pointer" />
            <Share2 className="h-6 w-6 text-gray-900 dark:text-white hover:text-gray-600 dark:hover:text-gray-300 cursor-pointer" />
          </div>
          <Bookmark className="h-6 w-6 text-gray-900 dark:text-white hover:text-gray-600 dark:hover:text-gray-300 cursor-pointer" />
        </div>

        {/* Likes */}
        <p className="text-sm font-semibold text-gray-900 dark:text-white mb-1">1,234 likes</p>

        {/* Caption */}
        <div className="text-sm text-gray-900 dark:text-white">
          <span className="font-semibold">socialsparkapp</span> {postData.caption}
        </div>

        {/* Hashtags */}
        {postData.hashtags && (
          <p className="text-sm text-blue-600 dark:text-blue-400 mt-1">{postData.hashtags}</p>
        )}

        {/* View comments */}
        <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">View all 42 comments</p>
        <p className="text-xs text-gray-400 dark:text-gray-500 mt-1 uppercase tracking-wide">2 HOURS AGO</p>
      </div>
    </div>
  );

  // X (Twitter)-style preview
  const renderTwitterPreview = () => (
    <div className="max-w-md mx-auto bg-white dark:bg-black border border-gray-200 dark:border-gray-800 rounded-2xl overflow-hidden shadow-lg">
      {/* Header */}
      <div className="flex p-3 bg-white dark:bg-black">
        <Avatar className="h-10 w-10 mr-3 flex-shrink-0">
          <AvatarImage src="/placeholder-avatar.jpg" />
          <AvatarFallback className="bg-black dark:bg-white text-white dark:text-black text-sm font-bold">SP</AvatarFallback>
        </Avatar>
        <div className="flex-1 min-w-0">
          <div className="flex items-center">
            <p className="font-bold text-sm text-gray-900 dark:text-white truncate">SocialSpark AI</p>
            <div className="w-4 h-4 ml-1 flex-shrink-0">
              <svg viewBox="0 0 24 24" className="fill-blue-500">
                <path d="M22.25 12c0-1.43-.88-2.67-2.19-3.34.46-1.39.2-2.9-.81-3.91s-2.52-1.27-3.91-.81c-.66-1.31-1.91-2.19-3.34-2.19s-2.67.88-3.33 2.19c-1.4-.46-2.91-.2-3.92.81s-1.26 2.52-.8 3.91c-1.31.67-2.2 1.91-2.2 3.34s.89 2.67 2.2 3.34c-.46 1.39-.21 2.9.8 3.91s2.52 1.26 3.91.81c.67 1.31 1.91 2.19 3.34 2.19s2.68-.88 3.34-2.19c1.39.45 2.9.2 3.91-.81s1.27-2.52.81-3.91c1.31-.67 2.19-1.91 2.19-3.34zm-11.71 4.2L6.8 12.46l1.41-1.42 2.26 2.26 4.8-5.23 1.47 1.36-6.2 6.77z"/>
              </svg>
            </div>
            <span className="text-gray-500 dark:text-gray-400 text-sm ml-1">@socialsparkapp · 2h</span>
          </div>
          <div className="mt-2">
            <p className="text-sm text-gray-900 dark:text-white whitespace-pre-wrap">{postData.caption}</p>
            {postData.hashtags && (
              <p className="text-sm text-blue-500 dark:text-blue-400 mt-1">{postData.hashtags}</p>
            )}
          </div>
        </div>
        <MoreHorizontal className="h-5 w-5 text-gray-500 dark:text-gray-400 flex-shrink-0" />
      </div>

      {/* Image */}
      <div className="px-3 pb-3">
        <div className="rounded-2xl overflow-hidden border border-gray-200 dark:border-gray-700">
          <AspectRatio ratio={getAspectRatio()}>
            <img
              src={postData.imageData}
              alt="X (Twitter) post"
              className="w-full h-full object-cover"
            />
          </AspectRatio>
        </div>
      </div>

      {/* Actions */}
      <div className="px-3 pb-3 flex justify-between text-gray-500 dark:text-gray-400 border-t border-gray-100 dark:border-gray-800 pt-3">
        <div className="flex items-center hover:text-blue-500 cursor-pointer">
          <MessageCircle className="h-4 w-4 mr-2" />
          <span className="text-xs">24</span>
        </div>
        <div className="flex items-center hover:text-green-500 cursor-pointer">
          <Repeat2 className="h-4 w-4 mr-2" />
          <span className="text-xs">12</span>
        </div>
        <div className="flex items-center hover:text-red-500 cursor-pointer">
          <Heart className="h-4 w-4 mr-2" />
          <span className="text-xs">142</span>
        </div>
        <div className="flex items-center hover:text-blue-500 cursor-pointer">
          <Eye className="h-4 w-4 mr-2" />
          <span className="text-xs">1.2K</span>
        </div>
        <div className="flex items-center hover:text-blue-500 cursor-pointer">
          <Share2 className="h-4 w-4" />
        </div>
      </div>
    </div>
  );

  // LinkedIn-style preview
  const renderLinkedinPreview = () => (
    <div className="max-w-md mx-auto bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden shadow-lg">
      {/* Header */}
      <div className="p-4 bg-white dark:bg-gray-900">
        <div className="flex items-start">
          <Avatar className="h-12 w-12 mr-3 flex-shrink-0">
            <AvatarImage src="/placeholder-avatar.jpg" />
            <AvatarFallback className="bg-blue-600 text-white text-sm font-bold">SP</AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <div className="flex items-center">
              <p className="font-semibold text-gray-900 dark:text-white">SocialSpark AI</p>
              <span className="ml-1 text-blue-600 dark:text-blue-400">• 1st</span>
            </div>
            <p className="text-xs text-gray-600 dark:text-gray-400">AI-powered social media content creation</p>
            <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 mt-1">
              <span>2h</span>
              <span className="mx-1">•</span>
              <span>🌎</span>
            </div>
          </div>
          <MoreHorizontal className="h-5 w-5 text-gray-600 dark:text-gray-400 flex-shrink-0" />
        </div>

        {/* Content */}
        <div className="mt-3">
          <p className="text-sm text-gray-900 dark:text-white whitespace-pre-wrap">{postData.caption}</p>
          {postData.hashtags && (
            <p className="text-sm text-blue-600 dark:text-blue-400 mt-2">{postData.hashtags}</p>
          )}
        </div>
      </div>

      {/* Image */}
      <AspectRatio ratio={getAspectRatio()}>
        <img
          src={postData.imageData}
          alt="LinkedIn post"
          className="w-full h-full object-cover"
        />
      </AspectRatio>

      {/* Engagement bar */}
      <div className="px-4 py-2 bg-white dark:bg-gray-900 border-b border-gray-100 dark:border-gray-800">
        <div className="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400">
          <div className="flex items-center">
            <div className="flex -space-x-1 mr-2">
              <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                <ThumbsUp className="w-2 h-2 text-white" />
              </div>
              <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                <span className="text-white text-xs">👏</span>
              </div>
            </div>
            <span>87 reactions</span>
          </div>
          <div className="flex space-x-3">
            <span>12 comments</span>
            <span>3 reposts</span>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="p-3 bg-white dark:bg-gray-900">
        <div className="flex justify-between">
          <button className="flex items-center justify-center flex-1 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded transition-colors">
            <ThumbsUp className="h-5 w-5 mr-2" />
            <span className="text-sm font-medium">Like</span>
          </button>
          <button className="flex items-center justify-center flex-1 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded transition-colors">
            <MessageSquare className="h-5 w-5 mr-2" />
            <span className="text-sm font-medium">Comment</span>
          </button>
          <button className="flex items-center justify-center flex-1 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded transition-colors">
            <Repeat2 className="h-5 w-5 mr-2" />
            <span className="text-sm font-medium">Repost</span>
          </button>
          <button className="flex items-center justify-center flex-1 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded transition-colors">
            <Send className="h-5 w-5 mr-2" />
            <span className="text-sm font-medium">Send</span>
          </button>
        </div>
      </div>
    </div>
  );

  // Facebook-style preview
  const renderFacebookPreview = () => (
    <div className="max-w-md mx-auto bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden shadow-lg">
      {/* Header */}
      <div className="p-4 bg-white dark:bg-gray-900">
        <div className="flex items-center">
          <Avatar className="h-10 w-10 mr-3">
            <AvatarImage src="/placeholder-avatar.jpg" />
            <AvatarFallback className="bg-blue-600 text-white text-sm font-bold">SP</AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <div className="flex items-center">
              <p className="font-semibold text-gray-900 dark:text-white">SocialSpark AI</p>
              <div className="w-4 h-4 ml-1 flex-shrink-0">
                <svg viewBox="0 0 24 24" className="fill-blue-500">
                  <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 3.95-.36.1-.74.15-1.13.15-.27 0-.54-.03-.8-.08.54 1.69 2.11 2.95 4 2.98-1.46 1.16-3.31 1.84-5.33 1.84-.35 0-.69-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                </svg>
              </div>
            </div>
            <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
              <span>2h</span>
              <span className="mx-1">•</span>
              <span>🌎</span>
            </div>
          </div>
          <MoreHorizontal className="h-5 w-5 text-gray-600 dark:text-gray-400" />
        </div>

        {/* Content */}
        <div className="mt-3">
          <p className="text-sm text-gray-900 dark:text-white whitespace-pre-wrap">{postData.caption}</p>
          {postData.hashtags && (
            <p className="text-sm text-blue-600 dark:text-blue-400 mt-2">{postData.hashtags}</p>
          )}
        </div>
      </div>

      {/* Image */}
      <AspectRatio ratio={getAspectRatio()}>
        <img
          src={postData.imageData}
          alt="Facebook post"
          className="w-full h-full object-cover"
        />
      </AspectRatio>

      {/* Engagement bar */}
      <div className="px-4 py-2 bg-white dark:bg-gray-900 border-b border-gray-100 dark:border-gray-800">
        <div className="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400">
          <div className="flex items-center">
            <div className="flex items-center mr-2">
              <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center mr-1">
                <ThumbsUp className="w-2 h-2 text-white" />
              </div>
              <div className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center mr-1">
                <Heart className="w-2 h-2 text-white" />
              </div>
            </div>
            <span>You and 156 others</span>
          </div>
          <div className="flex space-x-3">
            <span>23 comments</span>
            <span>8 shares</span>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="p-3 bg-white dark:bg-gray-900">
        <div className="flex justify-between">
          <button className="flex items-center justify-center flex-1 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded transition-colors">
            <ThumbsUp className="h-5 w-5 mr-2" />
            <span className="text-sm font-medium">Like</span>
          </button>
          <button className="flex items-center justify-center flex-1 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded transition-colors">
            <MessageSquare className="h-5 w-5 mr-2" />
            <span className="text-sm font-medium">Comment</span>
          </button>
          <button className="flex items-center justify-center flex-1 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded transition-colors">
            <Share2 className="h-5 w-5 mr-2" />
            <span className="text-sm font-medium">Share</span>
          </button>
        </div>
      </div>
    </div>
  );

  // Generic preview for other platforms
  const renderGenericPreview = () => (
    <div className="max-w-md mx-auto bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden shadow-lg">
      <div className="p-4 border-b border-gray-100 dark:border-gray-800 bg-white dark:bg-gray-900">
        <div className="flex items-center">
          <Avatar className="h-10 w-10 mr-3">
            <AvatarImage src="/placeholder-avatar.jpg" />
            <AvatarFallback className="bg-gray-600 text-white text-sm font-bold">SP</AvatarFallback>
          </Avatar>
          <div>
            <p className="font-semibold text-gray-900 dark:text-white">SocialSpark AI</p>
            <p className="text-xs text-gray-500 dark:text-gray-400">{postData.platform} • 2h ago</p>
          </div>
        </div>
      </div>

      <div className="p-4 bg-white dark:bg-gray-900">
        <p className="text-sm text-gray-900 dark:text-white mb-3 whitespace-pre-wrap">{postData.caption}</p>
        <div className="rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700">
          <AspectRatio ratio={getAspectRatio()}>
            <img
              src={postData.imageData}
              alt="Social media post"
              className="w-full h-full object-cover"
            />
          </AspectRatio>
        </div>
        {postData.hashtags && (
          <p className="mt-3 text-sm text-blue-600 dark:text-blue-400">{postData.hashtags}</p>
        )}
      </div>
    </div>
  );

  // Platform icon
  const getPlatformIcon = () => {
    switch (postData.platform.toLowerCase()) {
      case 'instagram':
        return <Instagram className="h-5 w-5 mr-2" />;
      case 'twitter':
      case 'x':
        return <XIcon className="h-5 w-5 mr-2" />;
      case 'linkedin':
        return <Linkedin className="h-5 w-5 mr-2" />;
      case 'facebook':
        return <Facebook className="h-5 w-5 mr-2" />;
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-950 transition-colors">
      <div className="container max-w-6xl py-8 px-4">
        <div className="flex justify-between items-center mb-6">
          <Button variant="outline" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-2" /> Back to Editor
          </Button>
          <div className="flex items-center">
            <span className="mr-4 flex items-center text-gray-900 dark:text-white">
              {getPlatformIcon()}
              {postData.platform.toLowerCase() === 'x' ? 'X (Twitter)' : postData.platform} Preview
            </span>
            <Button onClick={handleDownload}>
              <Download className="h-4 w-4 mr-2" /> Download Image
            </Button>
          </div>
        </div>

        <div className="flex justify-center">
          {renderPlatformPreview()}
        </div>
      </div>
    </div>
  );
}