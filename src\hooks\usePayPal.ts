import { useState, useEffect } from 'react';

interface PayPalConfig {
  clientId: string;
  environment: 'sandbox' | 'live';
}

export const usePayPal = () => {
  const [config, setConfig] = useState<PayPalConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchConfig = async () => {
      try {
        const response = await fetch('/api/payments/paypal/config');
        if (response.ok) {
          const data = await response.json();
          setConfig(data);
        } else {
          throw new Error('Failed to fetch PayPal configuration');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load PayPal');
        console.error('PayPal config error:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchConfig();
  }, []);

  return { config, loading, error };
};