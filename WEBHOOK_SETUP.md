# PayPal Webhook Setup Guide

This guide explains how to set up PayPal webhooks to automatically handle subscription cancellations and payment failures.

## Features Implemented

### 1. Automatic Subscription Cancellation Sync
- When users cancel their subscription directly from PayPal, the webhook automatically updates your database
- User keeps their current plan benefits until the next billing date, then gets downgraded to free
- Same behavior as cancelling through your website

### 2. Immediate Payment Failure Handling
- When a payment fails, the user is immediately downgraded to the free plan
- No retries are attempted (as requested)
- Subscription is cancelled automatically

### 3. Scheduled Subscription Maintenance
- Runs every hour to check subscription statuses with PayPal
- Processes expired cancelled subscriptions
- Backup system in case webhooks fail

## Webhook Events Handled

- `BILLING.SUBSCRIPTION.CANCELLED` - User cancelled subscription from PayPal
- `BILLING.SUBSCRIPTION.SUSPENDED` - Subscription suspended by PayPal
- `PAYMENT.SALE.DENIED` - Payment was denied
- `BILLING.SUBSCRIPTION.PAYMENT.FAILED` - Subscription payment failed

## Setup Instructions

### 1. PayPal Developer Dashboard Setup

1. Go to [PayPal Developer Dashboard](https://developer.paypal.com/developer/applications/)
2. Select your application
3. Go to "Webhooks" section
4. Click "Add Webhook"
5. Set webhook URL to: `https://yourdomain.com/api/webhooks/paypal`
6. Select these events:
   - `BILLING.SUBSCRIPTION.CANCELLED`
   - `BILLING.SUBSCRIPTION.SUSPENDED`
   - `PAYMENT.SALE.DENIED`
   - `BILLING.SUBSCRIPTION.PAYMENT.FAILED`
7. Save the webhook and copy the Webhook ID

### 2. Environment Variables

Add to your `.env` file:
```bash
PAYPAL_WEBHOOK_ID=your_webhook_id_here
```

### 3. Update PayPal Plans (Optional)

If you want to recreate your PayPal plans with the new no-retry settings:

```bash
node setup-paypal-plans.js
```

This will create plans with:
- `auto_bill_outstanding: false` - No automatic retries
- `payment_failure_threshold: 1` - Fail immediately
- `setup_fee_failure_action: "CANCEL"` - Cancel on failure

## Testing

### Test Webhook Locally

1. Use ngrok to expose your local server:
   ```bash
   ngrok http 3001
   ```

2. Update your PayPal webhook URL to the ngrok URL:
   ```
   https://your-ngrok-url.ngrok.io/api/webhooks/paypal
   ```

3. Test subscription cancellation from PayPal sandbox

### Manual Sync (Admin Only)

You can manually trigger subscription sync via:
```
POST /api/admin/sync-subscriptions
```

## Monitoring

The system logs all webhook events and sync operations. Check your server logs for:

- `PayPal webhook received` - Webhook endpoint hit
- `Webhook processed successfully` - Event handled
- `Running scheduled subscription maintenance` - Hourly sync
- `Subscription sync completed` - Sync finished

## Security

- Webhook signature verification is implemented (basic validation)
- Admin-only endpoints for manual sync
- All webhook events are logged for audit

## Troubleshooting

### Webhook Not Receiving Events
1. Check PayPal webhook configuration
2. Verify webhook URL is accessible
3. Check server logs for errors

### Subscription Not Syncing
1. Check PayPal API credentials
2. Verify subscription IDs in database
3. Run manual sync via admin endpoint

### Payment Failures Not Processed
1. Verify webhook events are configured
2. Check webhook signature validation
3. Review server logs for processing errors

## Files Modified

- `server/index.js` - Added webhook endpoints and scheduled tasks
- `server/paypal.js` - Added webhook verification and subscription checking
- `server/webhook-handlers.js` - New webhook event handlers
- `server/subscription-sync.js` - New scheduled sync functionality
- `setup-paypal-plans.js` - Updated to disable payment retries

## API Endpoints Added

- `POST /api/webhooks/paypal` - PayPal webhook endpoint
- `POST /api/admin/sync-subscriptions` - Manual subscription sync (admin only)
