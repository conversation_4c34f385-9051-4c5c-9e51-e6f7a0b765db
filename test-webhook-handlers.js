// Test script for webhook handlers
import { handleWebhookEvent } from './server/webhook-handlers.js';

// Mock subscription cancelled event
const mockSubscriptionCancelledEvent = {
  event_type: 'BILLING.SUBSCRIPTION.CANCELLED',
  resource: {
    id: 'I-TEST123456789'
  }
};

// Mock payment failed event
const mockPaymentFailedEvent = {
  event_type: 'PAYMENT.SALE.DENIED',
  resource: {
    billing_agreement_id: 'I-TEST123456789'
  }
};

// Mock subscription suspended event
const mockSubscriptionSuspendedEvent = {
  event_type: 'BILLING.SUBSCRIPTION.SUSPENDED',
  resource: {
    id: 'I-TEST123456789'
  }
};

async function testWebhookHandlers() {
  console.log('Testing webhook handlers...\n');

  try {
    // Test subscription cancelled
    console.log('1. Testing subscription cancelled handler:');
    const cancelResult = await handleWebhookEvent(
      mockSubscriptionCancelledEvent.event_type,
      mockSubscriptionCancelledEvent
    );
    console.log('Result:', cancelResult);
    console.log('');

    // Test payment failed
    console.log('2. Testing payment failed handler:');
    const paymentResult = await handleWebhookEvent(
      mockPaymentFailedEvent.event_type,
      mockPaymentFailedEvent
    );
    console.log('Result:', paymentResult);
    console.log('');

    // Test subscription suspended
    console.log('3. Testing subscription suspended handler:');
    const suspendedResult = await handleWebhookEvent(
      mockSubscriptionSuspendedEvent.event_type,
      mockSubscriptionSuspendedEvent
    );
    console.log('Result:', suspendedResult);
    console.log('');

    // Test unknown event
    console.log('4. Testing unknown event handler:');
    const unknownResult = await handleWebhookEvent('UNKNOWN.EVENT.TYPE', {});
    console.log('Result:', unknownResult);

  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run tests
testWebhookHandlers();
