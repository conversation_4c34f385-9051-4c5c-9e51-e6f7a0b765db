import { createContext, useContext, useEffect, useState } from 'react';
import {
  registerUser as apiRegisterUser,
  loginUser as apiLoginUser,
  logout<PERSON>ser as apiLogoutUser,
  getUserProfile as apiGetUserProfile,
  updateUserProfile as apiUpdateUserProfile,
  refreshUserProfile as apiRefreshUserProfile,
  getUser
} from '@/lib/auth';

// Define types for our MySQL database
type Profile = {
  id: string;
  created_at: string;
  updated_at: string;
  full_name: string | null;
  plan: 'free' | 'basic' | 'pro' | 'ultra';
  posts_count: number;
  posts_limit: number;
  paypal_subscription_id?: string;
  paypal_order_id?: string;
  subscription_status?: string;
  next_billing_date?: string;
  billing_cycle?: string;
  is_admin?: number;
};

type User = {
  id: string;
  email: string;
  profile?: Profile;
};

type AuthContextType = {
  user: User | null;
  profile: Profile | null;
  loading: boolean;
  isAdmin: boolean;
  signUp: (email: string, password: string, fullName: string) => Promise<{ error: any }>;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signOut: () => Promise<{ error: any }>;
  updateProfile: (updates: Partial<Profile>) => Promise<{ error: any }>;
  refreshProfile: () => Promise<{ error: any, success: boolean }>;
  checkPostsLimit: () => boolean;
  incrementPostsCount: () => Promise<{ error: any, success: boolean }>;
  checkAdminStatus: () => Promise<boolean>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    console.log('AuthContext: Initializing');

    // Check if user is already logged in
    const storedUser = getUser();
    if (storedUser) {
      console.log('AuthContext: User found in localStorage');
      setUser(storedUser);

      // Always fetch fresh profile data on app load/refresh to get latest plan
      console.log('AuthContext: Fetching fresh profile data');
      fetchProfile().then(() => {
        // Check admin status after profile is loaded
        setTimeout(() => checkAdminStatus(), 200);
      });
    }

    setLoading(false);
  }, []);

  const fetchProfile = async () => {
    console.log('fetchProfile: Starting');
    setLoading(true);

    try {
      const { data, error } = await apiGetUserProfile();

      if (error) {
        console.error('fetchProfile: Error fetching profile:', error);
        return;
      }

      if (data) {
        console.log('fetchProfile: Profile found successfully', data);
        setProfile(data);
        setIsAdmin(!!data.is_admin);

        // Update the user object with the profile
        if (user) {
          const updatedUser = { ...user, profile: data };
          setUser(updatedUser);
        }
      }
    } catch (err) {
      console.error('fetchProfile: Unexpected error:', err);
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (email: string, password: string, fullName: string) => {
    setLoading(true);
    try {
      const { data, error } = await apiRegisterUser(email, password, fullName);

      if (error) {
        return { error };
      }

      if (data) {
        setUser(data.user);
        if (data.user.profile) {
          setProfile(data.user.profile);
          setIsAdmin(!!data.user.profile.is_admin);
        }
      }

      return { error: null };
    } catch (err) {
      console.error('Signup error:', err);
      return { error: err };
    } finally {
      setLoading(false);
    }
  };

  const signIn = async (email: string, password: string) => {
    setLoading(true);
    try {
      const { data, error } = await apiLoginUser(email, password);

      if (error) {
        return { error };
      }

      if (data) {
        setUser(data.user);
        if (data.user.profile) {
          setProfile(data.user.profile);
          setIsAdmin(!!data.user.profile.is_admin);
          // Double-check admin status via API
          setTimeout(() => checkAdminStatus(), 100);
        }
      }

      return { error: null };
    } catch (err) {
      console.error('Error signing in:', err);
      return { error: err as any };
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    console.log('signOut: Starting logout');
    setLoading(true);

    try {
      const { error } = apiLogoutUser();

      if (error) {
        console.error('signOut: Error signing out:', error);
        throw error;
      }

      console.log('signOut: Successfully signed out, clearing state');
      setUser(null);
      setProfile(null);
      setIsAdmin(false);
      return { error: null };
    } catch (err) {
      console.error('signOut: Error:', err);
      // Still clear state even if there's an error
      setUser(null);
      setProfile(null);
      setIsAdmin(false);
      return { error: err };
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (updates: Partial<Profile>) => {
    if (!user) return { error: new Error('User not authenticated') };

    try {
      const { data, error } = await apiUpdateUserProfile(updates);

      if (error) {
        return { error };
      }

      if (data) {
        setProfile(data);

        // Update the user object with the updated profile
        if (user) {
          const updatedUser = { ...user, profile: data };
          setUser(updatedUser);
        }
      }

      return { error: null };
    } catch (err) {
      return { error: err };
    }
  };

  const checkPostsLimit = () => {
    if (!profile) return false;
    return profile.posts_count < profile.posts_limit;
  };

  const incrementPostsCount = async () => {
    console.log('Incrementing posts count...');
    if (!user || !profile) {
      console.log('No user or profile found');
      return { error: new Error('User not authenticated'), success: false };
    }

    if (profile.posts_count >= profile.posts_limit) {
      console.log('Posts limit reached');
      return { error: new Error('Posts limit reached'), success: false };
    }

    console.log('Current posts count:', profile.posts_count);

    try {
      const newCount = profile.posts_count + 1;
      console.log('Updating to new count:', newCount);

      const { error } = await updateProfile({ posts_count: newCount });

      if (error) {
        console.error('Error updating posts count:', error);
        return { error, success: false };
      }

      return { error: null, success: true };
    } catch (err) {
      console.error('Unexpected error in incrementPostsCount:', err);
      return { error: err, success: false };
    }
  };

  const refreshProfile = async () => {
    console.log('Refreshing profile from server...');
    if (!user) {
      console.log('No user found, cannot refresh profile');
      return { error: new Error('User not authenticated'), success: false };
    }

    // Prevent multiple simultaneous refresh calls
    if (isRefreshing) {
      console.log('Profile refresh already in progress, skipping');
      return { error: null, success: true };
    }

    try {
      setIsRefreshing(true);
      const { data, error } = await apiRefreshUserProfile();

      if (error) {
        console.error('Error refreshing profile:', error);
        return { error, success: false };
      }

      if (data) {
        console.log('Profile refreshed successfully:', data);
        setProfile(data);

        // Update the user object with the refreshed profile
        const updatedUser = { ...user, profile: data };
        setUser(updatedUser);
      }

      return { error: null, success: true };
    } catch (err) {
      console.error('Unexpected error in refreshProfile:', err);
      return { error: err, success: false };
    } finally {
      setIsRefreshing(false);
    }
  };

  const checkAdminStatus = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      if (!token) return false;

      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || '';
      const response = await fetch(`${apiBaseUrl}/api/admin/status`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Admin status check result:', data.isAdmin);
        setIsAdmin(data.isAdmin);
        return data.isAdmin;
      }

      return false;
    } catch (error) {
      console.error('Error checking admin status:', error);
      return false;
    }
  };

  const value = {
    user,
    profile,
    loading,
    isAdmin,
    signUp,
    signIn,
    signOut,
    updateProfile,
    refreshProfile,
    checkPostsLimit,
    incrementPostsCount,
    checkAdminStatus
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};