
import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card"
import { Link, useNavigate } from "react-router-dom"
import { ArrowRight, Check, LogOut } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { useAuth } from "@/context/AuthContext"

export default function Account() {
  const { toast } = useToast();
  const { user, profile, signOut, loading, refreshProfile: contextRefreshProfile } = useAuth();
  const navigate = useNavigate();
  const [hasRefreshedOnMount, setHasRefreshedOnMount] = useState(false);
  const [loadingTimedOut, setLoadingTimedOut] = useState(false);

  // Function to manually refresh profile data
  const refreshProfile = async () => {
    if (!user) return;

    try {
      // Use the refreshProfile function from AuthContext
      const { error, success } = await contextRefreshProfile();

      if (error) {
        throw error;
      }

      if (success) {
        toast({
          title: 'Account refreshed',
          description: 'Your account data has been updated',
        });
      }
    } catch (err) {
      console.error('Unexpected error refreshing profile:', err);
      toast({
        title: 'Refresh failed',
        description: 'Could not refresh account data',
        variant: 'destructive'
      });
    }
  };

  // Main auth and navigation logic
  useEffect(() => {
    console.log('Account page - Auth state:', { user: !!user, profile: !!profile, loading });
    if (!loading && !user) {
      console.log('Account page - No user, redirecting to login');
      navigate("/login");
    }
  }, [user, profile, loading, navigate]);

  // Refresh profile data when Account page loads to ensure latest plan info
  useEffect(() => {
    if (user && !loading && !hasRefreshedOnMount) {
      console.log('Account: Refreshing profile to ensure latest plan data');
      setHasRefreshedOnMount(true);
      contextRefreshProfile().then(({ success, error }) => {
        if (success) {
          console.log('Account: Profile refreshed successfully');
        } else {
          console.error('Account: Failed to refresh profile:', error);
        }
      });
    }
  }, [user, loading, contextRefreshProfile, hasRefreshedOnMount]);

  // Check if profile needs refresh (if name contains [PLAN UPDATED])
  useEffect(() => {
    if (profile && profile.full_name && profile.full_name.includes('[PLAN UPDATED]')) {
      console.log('Account: Detected plan update notification, refreshing profile');
      
      // Remove the [PLAN UPDATED] flag immediately to prevent loops
      const cleanName = profile.full_name.replace(/\s*\[PLAN UPDATED\]\s*/g, '').trim();
      
      contextRefreshProfile().then(({ success }) => {
        if (success) {
          toast({
            title: "Plan Updated",
            description: "Your subscription plan has been updated",
          });
          
          // Update the profile name to remove the flag
          if (cleanName !== profile.full_name) {
            // This will be handled by the refreshProfile call above
            console.log('Plan update flag will be cleaned on next refresh');
          }
        }
      });
    }
  }, [profile, contextRefreshProfile, toast]);

  // Loading timeout logic
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (loading && !loadingTimedOut) {
      timer = setTimeout(() => {
        console.log('Account loading timed out after 8 seconds');
        setLoadingTimedOut(true);
      }, 8000);
    }
    return () => clearTimeout(timer);
  }, [loading, loadingTimedOut]);

  // Handle retry logic for missing profile (but don't auto-reload)
  useEffect(() => {
    if (!loading && !user) {
      console.log('No user, redirecting to login');
      navigate("/login");
    }
  }, [loading, user, navigate]);

  if (loading && !loadingTimedOut) {
    return <div className="section-padding flex justify-center items-center min-h-[300px]">
      <div className="text-center">
        <div className="animate-spin text-3xl mb-4">◌</div>
        <p>Loading your account information...</p>
      </div>
    </div>;
  }

  if (loading && loadingTimedOut) {
    return <div className="section-padding flex justify-center items-center min-h-[300px]">
      <div className="text-center">
        <h2 className="text-xl font-bold mb-4">Loading Timeout</h2>
        <p className="mb-4">Account is taking too long to load.</p>
        <div className="flex justify-center gap-3">
          <Button onClick={() => window.location.reload()} variant="default">Refresh Page</Button>
          <Button onClick={() => {
            localStorage.clear(); // Clear all local storage
            navigate("/login");
          }} variant="outline">Return to Login</Button>
        </div>
      </div>
    </div>;
  }

  if (!user || !profile) {
    return (
      <div className="section-padding">
        <div className="max-w-md mx-auto p-6 bg-card rounded-lg shadow-sm border text-center">
          <h2 className="text-xl font-bold mb-4">Loading Account</h2>
          {user && !profile ? (
            <>
              <p className="mb-4">Retrieving your profile information...</p>
              <div className="animate-spin text-3xl mb-4">◌</div>
              <div className="mt-4">
                <Button onClick={() => window.location.reload()} variant="default">Retry</Button>
                <Button onClick={() => navigate("/login")} variant="outline" className="ml-2">Return to Login</Button>
              </div>
            </>
          ) : (
            <>
              <p className="mb-4">There was a problem loading your account information.</p>
              <Button onClick={() => navigate("/login")} variant="default">Return to Login</Button>
            </>
          )}
        </div>
      </div>
    );
  }

  const getPlanDetails = () => {
    switch (profile.plan) {
      case 'free':
        return { limit: 3, name: 'Free', rateLimit: 1 };
      case 'basic':
        return { limit: 50, name: 'Basic', rateLimit: 1 };
      case 'pro':
        return { limit: 200, name: 'Pro', rateLimit: 3 };
      case 'ultra':
        return { limit: 500, name: 'Ultra', rateLimit: 5 };
      default:
        return { limit: 3, name: 'Free', rateLimit: 1 };
    }
  };

  const planDetails = getPlanDetails();
  const postsLeft = profile.posts_limit - profile.posts_count;

  const handleLogout = async () => {
    await signOut();
    toast({
      title: "Logged out",
      description: "You have been successfully logged out."
    });
    navigate("/");
  };

  const handleCancelSubscription = async () => {
    if (!profile.paypal_subscription_id) {
      toast({
        title: "No subscription found",
        description: "You don't have an active subscription to cancel.",
        variant: "destructive"
      });
      return;
    }

    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/payments/paypal/subscription-cancel', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          subscriptionID: profile.paypal_subscription_id
        })
      });

      if (response.ok) {
        await contextRefreshProfile();
        toast({
          title: "Subscription cancelled",
          description: "Your subscription has been cancelled successfully."
        });
      } else {
        throw new Error('Failed to cancel subscription');
      }
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      toast({
        title: "Cancellation failed",
        description: "Could not cancel your subscription. Please try again.",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="flex flex-col min-h-screen">
      <section className="section-padding">
        <div className="max-w-4xl mx-auto">
          <div className="flex justify-between items-center mb-8">
            <div className="flex items-center gap-2">
              <h1 className="text-4xl font-bold">My Account</h1>
              <Button
                variant="ghost"
                size="sm"
                onClick={refreshProfile}
                className="ml-2 text-sm text-muted-foreground"
                title="Refresh account data"
              >
                ↻ Refresh
              </Button>
            </div>
            <Button variant="ghost" size="sm" onClick={handleLogout} className="flex items-center gap-2">
              <LogOut className="h-4 w-4" />
              Logout
            </Button>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="md:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle>Subscription Details</CardTitle>
                  <CardDescription>Manage your current subscription plan</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Current Plan</p>
                      <p className="font-medium">{planDetails.name}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Status</p>
                      <p className="font-medium">Active</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Email</p>
                      <p className="font-medium">{user.email}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Posts Remaining</p>
                      <p className="font-medium">{postsLeft} this month</p>
                    </div>
                  </div>

                  <div className="pt-4 border-t">
                    <h3 className="text-lg font-medium mb-3">Plan Features</h3>
                    <ul className="space-y-2">
                      <li className="flex items-start gap-3">
                        <Check className="h-5 w-5 text-primary shrink-0 mt-0.5" />
                        <span>{planDetails.limit} posts/month</span>
                      </li>
                      {profile.plan !== 'free' && (
                        <li className="flex items-start gap-3">
                          <Check className="h-5 w-5 text-primary shrink-0 mt-0.5" />
                          <span>Email support</span>
                        </li>
                      )}
                      {(profile.plan === 'pro' || profile.plan === 'ultra') && (
                        <li className="flex items-start gap-3">
                          <Check className="h-5 w-5 text-primary shrink-0 mt-0.5" />
                          <span>Queue priority</span>
                        </li>
                      )}
                    </ul>
                  </div>
                </CardContent>
                <CardFooter className="flex flex-col sm:flex-row gap-3">
                  {/* Show cancel button only for active subscriptions (not one-time purchases) */}
                  {profile.plan !== 'free' && 
                   profile.subscription_status === 'active' && 
                   profile.paypal_subscription_id && 
                   !profile.paypal_order_id && (
                    <Button variant="destructive" onClick={handleCancelSubscription} className="w-full sm:w-auto">
                      Cancel Subscription
                    </Button>
                  )}
                  
                  {/* Show status message for cancelled subscriptions */}
                  {profile.subscription_status === 'cancelled' && profile.next_billing_date && (
                    <div className="text-sm text-muted-foreground">
                      Subscription cancelled. Benefits active until {new Date(profile.next_billing_date).toLocaleDateString()}
                    </div>
                  )}
                  
                  {/* Show status message for one-time purchases */}
                  {profile.plan !== 'free' && 
                   profile.paypal_order_id && 
                   !profile.paypal_subscription_id && 
                   profile.next_billing_date && (
                    <div className="text-sm text-muted-foreground">
                      Benefits active until {new Date(profile.next_billing_date).toLocaleDateString()}
                    </div>
                  )}
                </CardFooter>
              </Card>
            </div>

            <div>
              <Card>
                <CardHeader>
                  <CardTitle>Usage Summary</CardTitle>
                  <CardDescription>Current month activity</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Posts Generated</p>
                    <p className="text-2xl font-bold">
                      {profile.posts_count}
                      <span className="text-sm font-normal text-muted-foreground ml-1">/
                        {planDetails.limit}
                      </span>
                    </p>
                  </div>

                  <div className="bg-muted p-4 rounded-lg">
                    <p className="text-sm font-medium">Need more posts?</p>
                    <p className="text-sm text-muted-foreground mb-3">Upgrade your plan for increased capacity</p>
                    <Button variant="outline" size="sm" asChild className="w-full">
                      <Link to="/pricing" className="flex items-center justify-center">
                        View Plans
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
