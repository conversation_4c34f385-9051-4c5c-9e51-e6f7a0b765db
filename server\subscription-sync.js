import { isSubscriptionActive } from './paypal.js';
import { updateUserProfile } from './sqlite-auth.js';

// Sync subscription statuses with PayPal
export const syncSubscriptionStatuses = async () => {
  try {
    console.log('Starting subscription status sync...');
    
    // Get all users with active subscriptions
    const usersWithSubscriptions = await getUsersWithActiveSubscriptions();
    
    if (usersWithSubscriptions.length === 0) {
      console.log('No users with active subscriptions found');
      return { success: true, processed: 0 };
    }

    console.log(`Found ${usersWithSubscriptions.length} users with active subscriptions to check`);
    
    let processed = 0;
    let cancelled = 0;
    let errors = 0;

    for (const user of usersWithSubscriptions) {
      try {
        console.log(`Checking subscription status for user ${user.id} (subscription: ${user.paypal_subscription_id})`);
        
        // Check if subscription is still active with PayPal
        const isActive = await isSubscriptionActive(user.paypal_subscription_id);
        
        if (!isActive) {
          console.log(`Subscription ${user.paypal_subscription_id} is no longer active, cancelling locally`);
          
          // Cancel subscription locally (same as website cancellation)
          const updates = {
            subscription_status: 'cancelled',
            updated_at: new Date().toISOString()
            // Keep current plan benefits until next_billing_date
          };

          const result = await updateUserProfile(user.id, updates);
          
          if (result.error) {
            console.error(`Failed to update user ${user.id}:`, result.error);
            errors++;
          } else {
            console.log(`Successfully cancelled subscription for user ${user.id}`);
            cancelled++;
          }
        } else {
          console.log(`Subscription ${user.paypal_subscription_id} is still active`);
        }
        
        processed++;
        
        // Add small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        console.error(`Error checking subscription for user ${user.id}:`, error);
        errors++;
        processed++;
      }
    }

    console.log(`Subscription sync completed: ${processed} processed, ${cancelled} cancelled, ${errors} errors`);
    
    return {
      success: true,
      processed,
      cancelled,
      errors
    };

  } catch (error) {
    console.error('Error during subscription sync:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Check for expired cancelled subscriptions and downgrade them
export const processExpiredSubscriptions = async () => {
  try {
    console.log('Processing expired cancelled subscriptions...');
    
    // Get users with cancelled subscriptions that have passed their next_billing_date
    const expiredUsers = await getExpiredCancelledSubscriptions();
    
    if (expiredUsers.length === 0) {
      console.log('No expired cancelled subscriptions found');
      return { success: true, processed: 0 };
    }

    console.log(`Found ${expiredUsers.length} expired cancelled subscriptions to process`);
    
    let processed = 0;
    let errors = 0;

    for (const user of expiredUsers) {
      try {
        console.log(`Downgrading expired subscription for user ${user.id}`);
        
        // Downgrade to free plan
        const updates = {
          plan: 'free',
          posts_limit: 3,
          posts_count: 0,
          subscription_status: 'expired',
          billing_cycle: null,
          next_billing_date: null,
          paypal_subscription_id: null,
          updated_at: new Date().toISOString()
        };

        const result = await updateUserProfile(user.id, updates);
        
        if (result.error) {
          console.error(`Failed to downgrade user ${user.id}:`, result.error);
          errors++;
        } else {
          console.log(`Successfully downgraded user ${user.id} to free plan`);
        }
        
        processed++;
        
      } catch (error) {
        console.error(`Error processing expired subscription for user ${user.id}:`, error);
        errors++;
        processed++;
      }
    }

    console.log(`Expired subscription processing completed: ${processed} processed, ${errors} errors`);
    
    return {
      success: true,
      processed,
      errors
    };

  } catch (error) {
    console.error('Error processing expired subscriptions:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Helper function to get users with active subscriptions
const getUsersWithActiveSubscriptions = async () => {
  try {
    const Database = (await import('better-sqlite3')).default;
    const db = new Database('./data/socialspark.db');
    
    const stmt = db.prepare(`
      SELECT id, paypal_subscription_id, plan, subscription_status, next_billing_date
      FROM profiles 
      WHERE subscription_status = 'active' 
      AND paypal_subscription_id IS NOT NULL 
      AND paypal_subscription_id != ''
    `);
    
    const users = stmt.all();
    db.close();
    
    return users;
  } catch (error) {
    console.error('Error getting users with active subscriptions:', error);
    return [];
  }
};

// Helper function to get expired cancelled subscriptions
const getExpiredCancelledSubscriptions = async () => {
  try {
    const Database = (await import('better-sqlite3')).default;
    const db = new Database('./data/socialspark.db');
    
    const now = new Date().toISOString();
    
    const stmt = db.prepare(`
      SELECT id, plan, subscription_status, next_billing_date
      FROM profiles 
      WHERE subscription_status = 'cancelled' 
      AND next_billing_date IS NOT NULL 
      AND next_billing_date <= ?
      AND plan != 'free'
    `);
    
    const users = stmt.all(now);
    db.close();
    
    return users;
  } catch (error) {
    console.error('Error getting expired cancelled subscriptions:', error);
    return [];
  }
};

// Run both sync processes
export const runSubscriptionMaintenance = async () => {
  console.log('=== Starting subscription maintenance ===');
  
  const syncResult = await syncSubscriptionStatuses();
  const expiredResult = await processExpiredSubscriptions();
  
  console.log('=== Subscription maintenance completed ===');
  
  return {
    sync: syncResult,
    expired: expiredResult
  };
};
