#!/usr/bin/env node

import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';

dotenv.config();

const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret_key_here';
const userId = 'b46f5a98-1422-492e-8723-af8aca9771f2';

// Generate a token
const token = jwt.sign({ sub: userId }, JWT_SECRET, { expiresIn: '7d' });

console.log('Testing analytics endpoints...');

// Test the analytics endpoints
const endpoints = [
  '/api/admin/stats/overview',
  '/api/admin/stats/users-online',
  '/api/admin/stats/user-growth?period=30&groupBy=day',
  '/api/admin/stats/post-activity?period=30&groupBy=day',
  '/api/admin/stats/revenue?period=30&groupBy=day'
];

for (const endpoint of endpoints) {
  try {
    console.log(`\nTesting ${endpoint}...`);
    const response = await fetch(`http://localhost:5001${endpoint}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ ${endpoint} - Success`);
      console.log('Response:', JSON.stringify(data, null, 2));
    } else {
      console.log(`❌ ${endpoint} - Failed with status ${response.status}`);
      const error = await response.text();
      console.log('Error:', error);
    }
  } catch (error) {
    console.log(`❌ ${endpoint} - Error:`, error.message);
  }
}
