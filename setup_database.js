// Script to manually set up the MySQL database
import mysql from 'mysql2/promise';
import dotenv from 'dotenv';
import readline from 'readline';
import { promisify } from 'util';

// Load environment variables
dotenv.config();

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const question = promisify(rl.question).bind(rl);

async function setupDatabase() {
  console.log('SocialSparkGen Database Setup');
  console.log('============================');

  try {
    // Get MySQL connection details
    const host = await question(`MySQL Host (default: ${process.env.DB_HOST || 'localhost'}): `) || process.env.DB_HOST || 'localhost';
    const port = await question(`MySQL Port (default: ${process.env.DB_PORT || '3306'}): `) || process.env.DB_PORT || '3306';
    const user = await question(`MySQL Username (default: ${process.env.DB_USER || 'root'}): `) || process.env.DB_USER || 'root';
    const password = await question('MySQL Password: ') || process.env.DB_PASSWORD || '';
    const dbName = await question(`Database Name (default: ${process.env.DB_NAME || 'socialspark'}): `) || process.env.DB_NAME || 'socialspark';

    console.log('\nConnecting to MySQL...');

    // Connect to MySQL server (without database)
    const rootConnection = await mysql.createConnection({
      host,
      port,
      user,
      password
    });

    console.log('Connected to MySQL server');

    // Create database if it doesn't exist
    console.log(`Creating database ${dbName} if it doesn't exist...`);
    await rootConnection.query(`CREATE DATABASE IF NOT EXISTS ${dbName}`);

    // Switch to the database
    await rootConnection.query(`USE ${dbName}`);

    // Create tables
    console.log('Creating tables...');

    // Profiles table
    await rootConnection.query(`
      CREATE TABLE IF NOT EXISTS profiles (
        id VARCHAR(36) PRIMARY KEY,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        full_name VARCHAR(255),
        plan ENUM('free', 'basic', 'pro', 'ultra') DEFAULT 'free',
        posts_count INT DEFAULT 0,
        posts_limit INT DEFAULT 5
      )
    `);

    // Posts table
    await rootConnection.query(`
      CREATE TABLE IF NOT EXISTS posts (
        id VARCHAR(36) PRIMARY KEY,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        user_id VARCHAR(36) NOT NULL,
        content TEXT NOT NULL,
        caption TEXT,
        image_url TEXT,
        published BOOLEAN DEFAULT FALSE,
        FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE
      )
    `);

    // Users table
    await rootConnection.query(`
      CREATE TABLE IF NOT EXISTS users (
        id VARCHAR(36) PRIMARY KEY,
        email VARCHAR(255) NOT NULL UNIQUE,
        password_hash VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (id) REFERENCES profiles(id) ON DELETE CASCADE
      )
    `);

    // Create indexes
    console.log('Creating indexes...');
    // Check if index exists before creating it (MySQL compatible approach)
    try {
      const [indexExists] = await rootConnection.query(`
        SELECT COUNT(*) as count
        FROM information_schema.statistics
        WHERE table_schema = DATABASE()
        AND table_name = 'posts'
        AND index_name = 'idx_posts_user_id'
      `);

      if (indexExists[0].count === 0) {
        await rootConnection.query('CREATE INDEX idx_posts_user_id ON posts(user_id)');
        console.log('Created index idx_posts_user_id');
      } else {
        console.log('Index idx_posts_user_id already exists');
      }
    } catch (err) {
      console.log('Note: Could not create posts index:', err.message);
    }

    try {
      const [indexExists] = await rootConnection.query(`
        SELECT COUNT(*) as count
        FROM information_schema.statistics
        WHERE table_schema = DATABASE()
        AND table_name = 'users'
        AND index_name = 'idx_users_email'
      `);

      if (indexExists[0].count === 0) {
        await rootConnection.query('CREATE INDEX idx_users_email ON users(email)');
        console.log('Created index idx_users_email');
      } else {
        console.log('Index idx_users_email already exists');
      }
    } catch (err) {
      console.log('Note: Could not create users index:', err.message);
    }

    console.log('\nDatabase setup completed successfully!');

    // Update .env file with the new connection details
    console.log('\nWould you like to update your .env file with these connection details? (y/n)');
    const updateEnv = await question('> ');

    if (updateEnv.toLowerCase() === 'y') {
      // We'll just print the instructions for now
      console.log('\nPlease update your .env file with the following:');
      console.log(`
# MySQL Database Configuration
DB_HOST=${host}
DB_PORT=${port}
DB_NAME=${dbName}
DB_USER=${user}
DB_PASSWORD=${password}
      `);
    }

    await rootConnection.end();
    console.log('Connection closed');

  } catch (error) {
    console.error('Error setting up database:', error);
  } finally {
    rl.close();
  }
}

setupDatabase();
