import { updateUserProfile, getUserProfile } from './sqlite-auth.js';

// Handle subscription cancelled webhook
export const handleSubscriptionCancelled = async (eventData) => {
  try {
    console.log('Processing subscription cancelled webhook:', eventData);

    const subscriptionId = eventData.resource?.id;
    if (!subscriptionId) {
      console.error('No subscription ID found in webhook data');
      return { success: false, error: 'No subscription ID' };
    }

    // Find user by subscription ID
    const user = await findUserBySubscriptionId(subscriptionId);
    if (!user) {
      console.error(`No user found with subscription ID: ${subscriptionId}`);
      return { success: false, error: 'User not found' };
    }

    console.log(`Found user ${user.id} with cancelled subscription ${subscriptionId}`);

    // Update user profile - same logic as website cancellation
    const updates = {
      subscription_status: 'cancelled', // Mark as cancelled but keep current plan
      updated_at: new Date().toISOString()
      // Don't change plan, posts_limit, billing_cycle, or next_billing_date
      // User keeps benefits until next_billing_date
    };

    const result = await updateUserProfile(user.id, updates);

    if (result.error) {
      console.error('Failed to update user profile after webhook cancellation:', result.error);
      return { success: false, error: result.error };
    }

    console.log(`Webhook: User ${user.id} subscription marked as cancelled`);
    return {
      success: true,
      message: 'Subscription cancelled via webhook',
      userId: user.id
    };

  } catch (error) {
    console.error('Error handling subscription cancelled webhook:', error);
    return { success: false, error: error.message };
  }
};

// Handle payment failure webhook
export const handlePaymentFailed = async (eventData) => {
  try {
    console.log('Processing payment failed webhook:', eventData);

    const subscriptionId = eventData.resource?.billing_agreement_id ||
                          eventData.resource?.subscription_id;

    if (!subscriptionId) {
      console.error('No subscription ID found in payment failure webhook');
      return { success: false, error: 'No subscription ID' };
    }

    // Find user by subscription ID
    const user = await findUserBySubscriptionId(subscriptionId);
    if (!user) {
      console.error(`No user found with subscription ID: ${subscriptionId}`);
      return { success: false, error: 'User not found' };
    }

    console.log(`Payment failed for user ${user.id}, downgrading to free plan immediately`);

    // Immediately downgrade to free plan (no retries as requested)
    const updates = {
      plan: 'free',
      posts_limit: 3,
      posts_count: 0, // Reset count
      subscription_status: 'payment_failed',
      billing_cycle: null,
      next_billing_date: null,
      paypal_subscription_id: null,
      updated_at: new Date().toISOString()
    };

    const result = await updateUserProfile(user.id, updates);

    if (result.error) {
      console.error('Failed to downgrade user after payment failure:', result.error);
      return { success: false, error: result.error };
    }

    console.log(`Webhook: User ${user.id} downgraded to free plan due to payment failure`);
    return {
      success: true,
      message: 'User downgraded due to payment failure',
      userId: user.id
    };

  } catch (error) {
    console.error('Error handling payment failed webhook:', error);
    return { success: false, error: error.message };
  }
};

// Handle subscription suspended webhook
export const handleSubscriptionSuspended = async (eventData) => {
  try {
    console.log('Processing subscription suspended webhook:', eventData);

    const subscriptionId = eventData.resource?.id;
    if (!subscriptionId) {
      console.error('No subscription ID found in webhook data');
      return { success: false, error: 'No subscription ID' };
    }

    // Find user by subscription ID
    const user = await findUserBySubscriptionId(subscriptionId);
    if (!user) {
      console.error(`No user found with subscription ID: ${subscriptionId}`);
      return { success: false, error: 'User not found' };
    }

    console.log(`Subscription suspended for user ${user.id}, downgrading to free plan`);

    // Downgrade to free plan immediately
    const updates = {
      plan: 'free',
      posts_limit: 3,
      posts_count: 0,
      subscription_status: 'suspended',
      billing_cycle: null,
      next_billing_date: null,
      paypal_subscription_id: null,
      updated_at: new Date().toISOString()
    };

    const result = await updateUserProfile(user.id, updates);

    if (result.error) {
      console.error('Failed to downgrade user after suspension:', result.error);
      return { success: false, error: result.error };
    }

    console.log(`Webhook: User ${user.id} downgraded due to subscription suspension`);
    return {
      success: true,
      message: 'User downgraded due to subscription suspension',
      userId: user.id
    };

  } catch (error) {
    console.error('Error handling subscription suspended webhook:', error);
    return { success: false, error: error.message };
  }
};

// Helper function to find user by PayPal subscription ID
const findUserBySubscriptionId = async (subscriptionId) => {
  try {
    // Import database here to avoid circular dependencies
    const { getOne } = await import('./sqlite-db.js');

    const user = getOne('SELECT * FROM profiles WHERE paypal_subscription_id = ?', [subscriptionId]);

    return user;
  } catch (error) {
    console.error('Error finding user by subscription ID:', error);
    return null;
  }
};

// Main webhook event router
export const handleWebhookEvent = async (eventType, eventData) => {
  console.log(`Processing webhook event: ${eventType}`);

  switch (eventType) {
    case 'BILLING.SUBSCRIPTION.CANCELLED':
      return await handleSubscriptionCancelled(eventData);

    case 'PAYMENT.SALE.DENIED':
    case 'BILLING.SUBSCRIPTION.PAYMENT.FAILED':
      return await handlePaymentFailed(eventData);

    case 'BILLING.SUBSCRIPTION.SUSPENDED':
      return await handleSubscriptionSuspended(eventData);

    default:
      console.log(`Unhandled webhook event type: ${eventType}`);
      return { success: true, message: 'Event type not handled' };
  }
};
