import React from 'react';
import { PayPalScriptProvider } from '@paypal/react-paypal-js';
import { usePayPal } from '@/hooks/usePayPal';

interface PayPalProviderProps {
  children: React.ReactNode;
}

const PayPalProvider: React.FC<PayPalProviderProps> = ({ children }) => {
  const { config, loading, error } = usePayPal();

  // If still loading or there's an error, just render children without PayPal
  if (loading || error || !config) {
    if (error) {
      console.warn('PayPal configuration error:', error);
    }
    return <>{children}</>;
  }

  const paypalOptions = {
    clientId: config.clientId,
    currency: 'USD',
    intent: 'capture',
    components: 'buttons',
    vault: true  // Required for subscriptions
  };

  return (
    <PayPalScriptProvider options={paypalOptions}>
      {children}
    </PayPalScriptProvider>
  );
};

export default PayPalProvider;