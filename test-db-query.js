import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import Database from 'better-sqlite3';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Database setup
const dbPath = join(__dirname, 'data', 'socialspark.db');
const db = new Database(dbPath);

// Test the two different approaches to calculating posts in the last minute
function testPostsPerMinuteQueries() {
  console.log('🧪 Testing Posts/Minute query approaches...\n');

  try {
    // Approach 1: JavaScript Date calculation (NEW - what we fixed to)
    const oneMinuteAgo = new Date(Date.now() - 60 * 1000).toISOString();
    const jsApproachResult = db.prepare(`
      SELECT COUNT(*) as count
      FROM posts
      WHERE created_at >= ?
    `).get(oneMinuteAgo);

    // Approach 2: SQLite datetime function (OLD - what was causing issues)
    const sqliteApproachResult = db.prepare(`
      SELECT COUNT(*) as count
      FROM posts
      WHER<PERSON> created_at >= datetime('now', '-1 minute')
    `).get();

    console.log('📊 Query Results:');
    console.log(`   JavaScript approach: ${jsApproachResult.count} posts`);
    console.log(`   SQLite approach: ${sqliteApproachResult.count} posts`);
    console.log(`   Difference: ${Math.abs(jsApproachResult.count - sqliteApproachResult.count)} posts\n`);

    // Show the actual timestamps being used
    console.log('🕐 Timestamp Comparison:');
    console.log(`   JavaScript oneMinuteAgo: ${oneMinuteAgo}`);
    console.log(`   Current time: ${new Date().toISOString()}`);
    
    // Get SQLite's current time
    const sqliteNow = db.prepare("SELECT datetime('now') as now").get();
    const sqliteOneMinuteAgo = db.prepare("SELECT datetime('now', '-1 minute') as time").get();
    console.log(`   SQLite now: ${sqliteNow.now}`);
    console.log(`   SQLite oneMinuteAgo: ${sqliteOneMinuteAgo.time}\n`);

    // Get some recent posts to see their timestamps
    const recentPosts = db.prepare(`
      SELECT id, created_at
      FROM posts
      ORDER BY created_at DESC
      LIMIT 5
    `).all();

    console.log('📝 Recent Posts (last 5):');
    recentPosts.forEach((post, index) => {
      const postTime = new Date(post.created_at);
      const ageSeconds = (new Date() - postTime) / 1000;
      console.log(`   ${index + 1}. ${post.created_at} (${Math.round(ageSeconds)}s ago)`);
    });

    // Test with different time ranges
    console.log('\n⏱️  Posts in different time ranges:');
    
    const ranges = [
      { label: '30 seconds', seconds: 30 },
      { label: '1 minute', seconds: 60 },
      { label: '2 minutes', seconds: 120 },
      { label: '5 minutes', seconds: 300 }
    ];

    ranges.forEach(range => {
      const timeAgo = new Date(Date.now() - range.seconds * 1000).toISOString();
      const count = db.prepare(`
        SELECT COUNT(*) as count
        FROM posts
        WHERE created_at >= ?
      `).get(timeAgo).count;
      console.log(`   Last ${range.label}: ${count} posts`);
    });

    // Verify the fix is working
    if (jsApproachResult.count === sqliteApproachResult.count) {
      console.log('\n✅ GOOD: Both approaches return the same result');
    } else {
      console.log('\n⚠️  WARNING: Approaches return different results');
      console.log('   This suggests there might be timezone or precision differences');
    }

  } catch (error) {
    console.error('❌ Error testing queries:', error);
  } finally {
    db.close();
  }
}

// Insert a test post to verify timing
function insertTestPost() {
  console.log('📝 Inserting test post...\n');
  
  try {
    const testPostId = 'test-' + Date.now();
    const now = new Date().toISOString();
    
    db.prepare(`
      INSERT INTO posts (id, user_id, content, caption, created_at)
      VALUES (?, ?, ?, ?, ?)
    `).run(testPostId, 'test-user', '{"test": true}', 'Test post', now);
    
    console.log(`✅ Inserted test post: ${testPostId} at ${now}\n`);
    
    // Now test the queries
    testPostsPerMinuteQueries();
    
  } catch (error) {
    console.error('❌ Error inserting test post:', error);
    // Still run the query test even if insert fails
    testPostsPerMinuteQueries();
  }
}

// Run the test
insertTestPost();
