import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Create data directory if it doesn't exist
const dataDir = path.join(__dirname, '..', 'data');
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// Database file path
const dbPath = path.join(dataDir, 'socialspark.db');

// Create and initialize the database
const db = new Database(dbPath);

// Enable foreign keys
db.pragma('foreign_keys = ON');

// Test the connection
const testConnection = () => {
  try {
    // Execute a simple query to test the connection
    const result = db.prepare('SELECT 1 AS test').get();
    console.log('SQLite connection established successfully');
    return true;
  } catch (error) {
    console.error('Error connecting to SQLite:', error);
    return false;
  }
};

// Initialize database (create tables if they don't exist)
const initDatabase = () => {
  try {
    // Create profiles table
    db.prepare(`
      CREATE TABLE IF NOT EXISTS profiles (
        id TEXT PRIMARY KEY,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        full_name TEXT,
        plan TEXT DEFAULT 'free' CHECK(plan IN ('free', 'basic', 'pro', 'ultra')),
        posts_count INTEGER DEFAULT 0,
        posts_limit INTEGER DEFAULT 3,
        last_post_time TIMESTAMP,
        subscription_status TEXT DEFAULT 'active' CHECK(subscription_status IN ('active', 'cancelled', 'expired')),
        billing_cycle TEXT CHECK(billing_cycle IN ('monthly', 'yearly')),
        next_billing_date TIMESTAMP,
        paypal_order_id TEXT
      )
    `).run();

    // Create posts table
    db.prepare(`
      CREATE TABLE IF NOT EXISTS posts (
        id TEXT PRIMARY KEY,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        user_id TEXT NOT NULL,
        content TEXT NOT NULL,
        caption TEXT,
        image_url TEXT,
        published INTEGER DEFAULT 0,
        FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE
      )
    `).run();

    // Create users table for basic authentication
    db.prepare(`
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        email TEXT NOT NULL UNIQUE,
        password_hash TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (id) REFERENCES profiles(id) ON DELETE CASCADE
      )
    `).run();

    // Create indexes for better performance
    try {
      db.prepare('CREATE INDEX IF NOT EXISTS idx_posts_user_id ON posts(user_id)').run();
    } catch (err) {
      // Index might already exist, which is fine
      console.log('Note: posts index may already exist');
    }

    try {
      db.prepare('CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)').run();
    } catch (err) {
      // Index might already exist, which is fine
      console.log('Note: users index may already exist');
    }

    // Create user sessions table for tracking online users
    db.prepare(`
      CREATE TABLE IF NOT EXISTS user_sessions (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        session_token TEXT NOT NULL,
        last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        ip_address TEXT,
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE
      )
    `).run();

    // Create analytics events table for detailed tracking
    db.prepare(`
      CREATE TABLE IF NOT EXISTS analytics_events (
        id TEXT PRIMARY KEY,
        user_id TEXT,
        event_type TEXT NOT NULL,
        event_data TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE SET NULL
      )
    `).run();

    // Add new columns to existing profiles table if they don't exist
    try {
      // Check if the new columns exist
      const tableInfo = db.prepare("PRAGMA table_info(profiles)").all();
      const columnNames = tableInfo.map(col => col.name);

      if (!columnNames.includes('subscription_status')) {
        db.prepare('ALTER TABLE profiles ADD COLUMN subscription_status TEXT DEFAULT "active"').run();
        console.log('Added subscription_status column to profiles table');
      }

      if (!columnNames.includes('billing_cycle')) {
        db.prepare('ALTER TABLE profiles ADD COLUMN billing_cycle TEXT').run();
        console.log('Added billing_cycle column to profiles table');
      }

      if (!columnNames.includes('next_billing_date')) {
        db.prepare('ALTER TABLE profiles ADD COLUMN next_billing_date TIMESTAMP').run();
        console.log('Added next_billing_date column to profiles table');
      }

      if (!columnNames.includes('paypal_order_id')) {
        db.prepare('ALTER TABLE profiles ADD COLUMN paypal_order_id TEXT').run();
        console.log('Added paypal_order_id column to profiles table');
      }

      if (!columnNames.includes('paypal_subscription_id')) {
        db.prepare('ALTER TABLE profiles ADD COLUMN paypal_subscription_id TEXT').run();
        console.log('Added paypal_subscription_id column to profiles table');
      }
    } catch (err) {
      console.log('Note: Column addition may have failed (columns might already exist):', err.message);
    }

    console.log('SQLite database initialized successfully');
    return true;
  } catch (error) {
    console.error('Error initializing SQLite database:', error);
    return false;
  }
};

// Helper function to execute a query with parameters
const query = (sql, params = []) => {
  try {
    const stmt = db.prepare(sql);
    return stmt.run(params);
  } catch (error) {
    console.error('Error executing query:', error);
    throw error;
  }
};

// Helper function to get a single row
const getOne = (sql, params = []) => {
  try {
    const stmt = db.prepare(sql);
    return stmt.get(params);
  } catch (error) {
    console.error('Error executing query:', error);
    throw error;
  }
};

// Helper function to get multiple rows
const getAll = (sql, params = []) => {
  try {
    const stmt = db.prepare(sql);
    return stmt.all(params);
  } catch (error) {
    console.error('Error executing query:', error);
    throw error;
  }
};

// Analytics helper functions
export const trackUserSession = async (userId, sessionToken, ipAddress, userAgent) => {
  const { v4: uuidv4 } = await import('uuid');
  const sessionId = uuidv4();
  return query(`
    INSERT OR REPLACE INTO user_sessions (id, user_id, session_token, last_activity, ip_address, user_agent)
    VALUES (?, ?, ?, CURRENT_TIMESTAMP, ?, ?)
  `, [sessionId, userId, sessionToken, ipAddress, userAgent]);
};

export const updateUserActivity = (sessionToken) => {
  return query(`
    UPDATE user_sessions
    SET last_activity = CURRENT_TIMESTAMP
    WHERE session_token = ?
  `, [sessionToken]);
};

export const removeUserSession = (sessionToken) => {
  return query(`DELETE FROM user_sessions WHERE session_token = ?`, [sessionToken]);
};

export const getOnlineUsersCount = () => {
  try {
    // Consider users online if they were active in the last 5 minutes
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000).toISOString();
    const result = getOne(`
      SELECT COUNT(DISTINCT user_id) as count
      FROM user_sessions
      WHERE last_activity > ?
    `, [fiveMinutesAgo]);
    return result || { count: 0 };
  } catch (error) {
    console.error('Error getting online users count:', error);
    return { count: 0 };
  }
};

export const trackAnalyticsEvent = async (userId, eventType, eventData = null) => {
  const { v4: uuidv4 } = await import('uuid');
  const eventId = uuidv4();
  return query(`
    INSERT INTO analytics_events (id, user_id, event_type, event_data)
    VALUES (?, ?, ?, ?)
  `, [eventId, userId, eventType, JSON.stringify(eventData)]);
};

// Initialize the database
initDatabase();

export { db, testConnection, initDatabase, query, getOne, getAll };
