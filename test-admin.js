#!/usr/bin/env node

import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';

dotenv.config();

const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret_key_here';
const userId = 'b46f5a98-1422-492e-8723-af8aca9771f2';

// Generate a token
const token = jwt.sign({ sub: userId }, JWT_SECRET, { expiresIn: '7d' });

console.log('Generated token:', token);

// Test the admin status endpoint
try {
  const response = await fetch('http://localhost:5001/api/admin/status', {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  const data = await response.json();
  console.log('Admin status response:', data);
} catch (error) {
  console.error('Error testing admin endpoint:', error);
}