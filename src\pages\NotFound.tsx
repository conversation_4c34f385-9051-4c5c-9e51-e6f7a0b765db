
import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { useEffect } from "react";
import { useLocation } from "react-router-dom";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="flex flex-col min-h-screen items-center justify-center text-center px-4">
      <div className="w-20 h-20 rounded-full bg-gradient-to-br from-spark-indigo to-spark-violet flex items-center justify-center text-white text-4xl font-bold mb-6">
        404
      </div>
      <h1 className="text-3xl md:text-4xl font-bold mb-3">Page Not Found</h1>
      <p className="text-muted-foreground max-w-md mb-8">
        Oops! The page you're looking for doesn't exist or has been moved.
      </p>
      <div className="space-x-4">
        <Button asChild>
          <Link to="/">Back to Home</Link>
        </Button>
        <Button variant="outline" asChild>
          <Link to="/contact">Contact Support</Link>
        </Button>
      </div>
    </div>
  );
};

export default NotFound;
