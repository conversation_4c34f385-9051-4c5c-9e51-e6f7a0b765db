
import { useState } from "react"
import { Link, useLocation, useNavigate } from "react-router-dom"
import { ThemeToggle } from "./ThemeToggle"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { Menu, X, User, LogIn, UserPlus, Image, Shield } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { useAuth } from "@/context/AuthContext"

const navLinks = [
  { name: "Home", href: "/" },
  { name: "Features", href: "/features" },
  { name: "Examples", href: "/examples" },
  { name: "Pricing", href: "/pricing" },
  { name: "Contact", href: "/contact" },
]

export default function Navbar() {
  const [isOpen, setIsOpen] = useState(false)
  const location = useLocation()
  const navigate = useNavigate()
  const { toast } = useToast()
  const { user, signOut, isAdmin } = useAuth()

  const handleLogout = async () => {
    try {
      console.log('Logging out...')
      await signOut()
      toast({
        title: "Logged out",
        description: "You have been successfully logged out."
      })
      // Force reload to clear any state
      window.location.href = '/'
    } catch (error) {
      console.error('Logout error:', error)
      toast({
        title: "Logout failed",
        description: "There was a problem logging out.",
        variant: "destructive"
      })
    }
  }

  return (
    <nav className="w-full py-4 px-4 md:px-8 border-b z-50 sticky top-0 bg-background/95 backdrop-blur-sm transition-all duration-300">
      <div className="max-w-7xl mx-auto flex justify-between items-center">
        <Link to="/" className="flex items-center space-x-2">
          <img src="/logo.png" alt="SocialSparkGen Logo" className="h-8 w-8" />
          <span className="text-xl font-bold">SocialSparkGen</span>
        </Link>

        <div className="hidden md:flex items-center space-x-6">
          {navLinks.map((link) => (
            <Link
              key={link.name}
              to={link.href}
              className={cn(
                "text-sm font-medium transition-colors hover:text-primary",
                location.pathname === link.href
                  ? "text-primary"
                  : "text-muted-foreground"
              )}
            >
              {link.name}
            </Link>
          ))}
        </div>

        <div className="hidden md:flex items-center space-x-4">
          <ThemeToggle />

          {user ? (
            <>
              <Button variant="outline" size="sm" asChild>
                <Link to="/gallery">
                  <Image className="h-4 w-4 mr-2" />
                  Gallery
                </Link>
              </Button>
              {isAdmin && (
                <Button variant="outline" size="sm" asChild>
                  <Link to={`/${user.id}/admin_panel`}>
                    <Shield className="h-4 w-4 mr-2" />
                    Admin
                  </Link>
                </Button>
              )}
              <Button variant="outline" size="sm" onClick={handleLogout}>
                Logout
              </Button>
              <Button variant="outline" size="sm" asChild>
                <Link to="/account">
                  <User className="h-4 w-4 mr-2" />
                  Account
                </Link>
              </Button>
            </>
          ) : (
            <>
              <Button variant="outline" size="sm" asChild>
                <Link to="/login">
                  <LogIn className="h-4 w-4 mr-2" />
                  Login
                </Link>
              </Button>
              <Button variant="outline" size="sm" asChild>
                <Link to="/register">
                  <UserPlus className="h-4 w-4 mr-2" />
                  Register
                </Link>
              </Button>
              <Button className="gradient-button" asChild>
                <Link to="/register">Try It Free</Link>
              </Button>
            </>
          )}
        </div>

        <div className="md:hidden flex items-center space-x-4">
          <ThemeToggle />
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsOpen(!isOpen)}
            aria-label="Toggle menu"
          >
            {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </Button>
        </div>
      </div>

      {/* Mobile menu */}
      <div
        className={cn(
          "md:hidden absolute left-0 right-0 top-[73px] bg-background border-b z-40 px-4 py-3 shadow-lg transition-all duration-300 ease-in-out",
          isOpen ? "translate-y-0 opacity-100" : "-translate-y-full opacity-0"
        )}
      >
        <div className="flex flex-col space-y-4 pt-2 pb-3">
          {navLinks.map((link) => (
            <Link
              key={link.name}
              to={link.href}
              className={cn(
                "text-base font-medium px-2 py-2 rounded-md transition-colors",
                location.pathname === link.href
                  ? "text-primary bg-primary/10"
                  : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
              )}
              onClick={() => setIsOpen(false)}
            >
              {link.name}
            </Link>
          ))}

          {user ? (
            <>
              <Link
                to="/gallery"
                className="text-base font-medium px-2 py-2 rounded-md flex items-center"
                onClick={() => setIsOpen(false)}
              >
                <Image className="h-4 w-4 mr-2" />
                Gallery
              </Link>
              {isAdmin && (
                <Link
                  to={`/${user.id}/admin_panel`}
                  className="text-base font-medium px-2 py-2 rounded-md flex items-center"
                  onClick={() => setIsOpen(false)}
                >
                  <Shield className="h-4 w-4 mr-2" />
                  Admin Panel
                </Link>
              )}
              <Link
                to="/account"
                className="text-base font-medium px-2 py-2 rounded-md flex items-center"
                onClick={() => setIsOpen(false)}
              >
                <User className="h-4 w-4 mr-2" />
                Account
              </Link>
              <Button className="w-full" onClick={() => {
                handleLogout();
                setIsOpen(false);
              }}>
                Logout
              </Button>
            </>
          ) : (
            <>
              <Link
                to="/login"
                className="text-base font-medium px-2 py-2 rounded-md flex items-center"
                onClick={() => setIsOpen(false)}
              >
                <LogIn className="h-4 w-4 mr-2" />
                Login
              </Link>
              <Link
                to="/register"
                className="text-base font-medium px-2 py-2 rounded-md flex items-center"
                onClick={() => setIsOpen(false)}
              >
                <UserPlus className="h-4 w-4 mr-2" />
                Register
              </Link>
              <Button className="gradient-button w-full mt-2" asChild>
                <Link to="/register" onClick={() => setIsOpen(false)}>Try It Free</Link>
              </Button>
            </>
          )}
        </div>
      </div>
    </nav>
  )
}
