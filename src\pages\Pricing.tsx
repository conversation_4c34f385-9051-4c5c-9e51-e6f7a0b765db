
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Check, X, Sparkles, Zap, BarChart2 } from "lucide-react"
import { Link, useNavigate } from "react-router-dom"
import PayPalButton from "@/components/PayPalButton"
import PayPalSubscriptionButton from "@/components/PayPalSubscriptionButton"
import { useAuth } from "@/context/AuthContext"
import { usePayPal } from "@/hooks/usePayPal"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

interface Feature {
  text: string;
  available: boolean;
  icon: React.ReactNode;
}

interface Plan {
  name: string;
  description: string;
  priceMonthly: string;
  priceYearly: string;
  features: Feature[];
  buttonText: string;
  isPopular: boolean;
}

export default function Pricing() {
  const [billingCycle, setBillingCycle] = useState<"monthly" | "yearly">("monthly")
  const { user } = useAuth()
  const navigate = useNavigate()
  const { config: paypalConfig, error: paypalError } = usePayPal()

  const plans: Plan[] = [
    {
      name: "Free",
      description: "Try it out with limited posts",
      priceMonthly: "$0",
      priceYearly: "$0",
      features: [
        { text: "3 posts/month", available: true, icon: null },
        { text: "1 post per minute", available: true, icon: null },
        { text: "Fast mode", available: true, icon: <Zap className="h-4 w-4" /> },
        { text: "Balanced mode", available: true, icon: <BarChart2 className="h-4 w-4" /> },
        { text: "Quality mode", available: false, icon: <Sparkles className="h-4 w-4" /> },
        { text: "Email support", available: false, icon: null },
      ],
      buttonText: "Get Started",
      isPopular: false,
    },
    {
      name: "Basic",
      description: "Perfect for individual creators just getting started",
      priceMonthly: "$5",
      priceYearly: "$4",
      features: [
        { text: "50 posts/month", available: true, icon: null },
        { text: "1 post per minute", available: true, icon: null },
        { text: "Fast mode", available: true, icon: <Zap className="h-4 w-4" /> },
        { text: "Balanced mode", available: true, icon: <BarChart2 className="h-4 w-4" /> },
        { text: "Quality mode", available: true, icon: <Sparkles className="h-4 w-4" /> },
        { text: "Email support", available: true, icon: null },
      ],
      buttonText: "Get Started",
      isPopular: false,
    },
    {
      name: "Pro",
      description: "Ideal for serious creators and small businesses",
      priceMonthly: "$15",
      priceYearly: "$12",
      features: [
        { text: "200 posts/month", available: true, icon: null },
        { text: "3 posts per minute", available: true, icon: null },
        { text: "Fast mode", available: true, icon: <Zap className="h-4 w-4" /> },
        { text: "Balanced mode", available: true, icon: <BarChart2 className="h-4 w-4" /> },
        { text: "Quality mode", available: true, icon: <Sparkles className="h-4 w-4" /> },
        { text: "Email support", available: true, icon: null },
        { text: "Queue priority", available: true, icon: null },
      ],
      buttonText: "Subscribe to Pro",
      isPopular: true,
    },
    {
      name: "Ultra",
      description: "For power users who need maximum content capacity",
      priceMonthly: "$30",
      priceYearly: "$24",
      features: [
        { text: "500 posts/month", available: true, icon: null },
        { text: "5 posts per minute", available: true, icon: null },
        { text: "Fast mode", available: true, icon: <Zap className="h-4 w-4" /> },
        { text: "Balanced mode", available: true, icon: <BarChart2 className="h-4 w-4" /> },
        { text: "Quality mode", available: true, icon: <Sparkles className="h-4 w-4" /> },
        { text: "Email support", available: true, icon: null },
        { text: "Queue priority", available: true, icon: null },
      ],
      buttonText: "Subscribe to Ultra",
      isPopular: false,
    }
  ]

  const faqs = [
    {
      question: "How many posts can I generate?",
      answer: "Each plan comes with a set number of generations per month. Free includes 3 posts/month, Basic includes 50 posts/month, Pro offers 200 posts/month, and Ultra provides 500 posts/month. Each plan also has rate limits: Free and Basic (1 post/minute), Pro (3 posts/minute), and Ultra (5 posts/minute)."
    },
    {
      question: "What are the different quality modes?",
      answer: "We offer three quality modes: Fast (quick generation with good results), Balanced (optimal balance of speed and quality), and Quality (highest quality output but slower). The FREE plan only has access to Fast and Balanced modes, while all paid plans can use all three modes including Quality mode."
    },
    {
      question: "Can I change plans anytime?",
      answer: "Yes! You can upgrade, downgrade or cancel your plan at any time. Changes take effect at the start of your next billing cycle."
    },
    {
      question: "What happens if I reach my monthly limit?",
      answer: "You'll be notified when you're nearing your limit. Once reached, you can either upgrade your plan or wait until your limit refreshes at the start of your next billing cycle."
    },
    {
      question: "Do you offer refunds?",
      answer: "We offer partial refunds within 7 days based on your usage. Refunds are calculated by subtracting the value of used posts from your payment. For example, if you're on the Ultra plan ($30) and used 100 posts, you could receive up to $24 back. The maximum refund is 80% of your payment regardless of usage."
    }
  ]

  const handlePaymentSuccess = (details: any) => {
    // Refresh user profile and redirect to account
    setTimeout(() => {
      navigate('/account')
    }, 2000)
  }

  const handlePaymentError = (error: any) => {
    console.error('Payment error:', error)
  }

  const handleFreeSignup = () => {
    if (!user) {
      navigate('/register')
    } else {
      navigate('/create')
    }
  }

  const getPrice = (plan: Plan) => {
    return billingCycle === "monthly" ? plan.priceMonthly : plan.priceYearly
  }

  const getPriceValue = (plan: Plan) => {
    if (billingCycle === 'yearly') {
      // For yearly billing, calculate the actual yearly amount
      const monthlyEquivalent = parseFloat(plan.priceYearly.replace('$', ''));
      const yearlyTotal = monthlyEquivalent * 12;
      return yearlyTotal.toString();
    }
    const price = getPrice(plan)
    return price.replace('$', '')
  }

  return (
    <div className="flex flex-col min-h-screen">
      <section className="section-padding">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Flexible Plans for Any Creator</h1>
            <p className="text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto">
              Choose the perfect plan for your content needs. All plans include our core AI generation features.
              No contracts. Cancel anytime.
            </p>

            <div className="flex items-center justify-center mt-8 mb-4">
              <div className="bg-muted rounded-full p-1 flex items-center">
                <Button
                  variant={billingCycle === "monthly" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setBillingCycle("monthly")}
                  className={`rounded-full px-6 ${billingCycle === "monthly" ? "bg-white text-primary dark:bg-primary dark:text-primary-foreground" : ""}`}
                >
                  Monthly
                </Button>
                <Button
                  variant={billingCycle === "yearly" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setBillingCycle("yearly")}
                  className={`rounded-full px-6 ${billingCycle === "yearly" ? "bg-white text-primary dark:bg-primary dark:text-primary-foreground" : ""}`}
                >
                  Yearly (Save 20%)
                </Button>
              </div>
            </div>
          </div>

          <div className="grid md:grid-cols-4 gap-8">
            {plans.map((plan) => (
              <div
                key={plan.name}
                className={`bg-card rounded-xl shadow-sm border overflow-hidden ${
                  plan.isPopular ? "ring-2 ring-primary" : ""
                }`}
              >
                {plan.isPopular && (
                  <div className="bg-primary text-primary-foreground text-center py-2 text-sm font-medium">
                    Most Popular
                  </div>
                )}
                <div className="p-6">
                  <h3 className="text-2xl font-bold">{plan.name}</h3>
                  <p className="text-muted-foreground mt-2 mb-4">{plan.description}</p>
                  <div className="mb-6">
                  <span className="text-4xl font-bold">
                  {getPrice(plan)}
                  </span>
                  <span className="text-muted-foreground">
                  /mo{billingCycle === "yearly" ? " billed yearly" : ""}
                  </span>
                  </div>
                  
                  {plan.name === "Free" ? (
                  <Button 
                  className={`w-full ${plan.isPopular ? "gradient-button" : ""}`}
                  onClick={handleFreeSignup}
                  >
                  {plan.buttonText}
                  </Button>
                  ) : user && paypalConfig ? (
                  <Tabs defaultValue="one-time" className="w-full">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="one-time">One-time</TabsTrigger>
                    <TabsTrigger value="subscription">Subscribe</TabsTrigger>
                  </TabsList>
                  <TabsContent value="one-time" className="mt-4">
                    <PayPalButton
                        planName={plan.name}
                          amount={getPriceValue(plan)}
                        billingCycle={billingCycle}
                      onSuccess={handlePaymentSuccess}
                      onError={handlePaymentError}
                        className={`w-full ${plan.isPopular ? "gradient-button" : ""}`}
                    />
                      <p className="text-xs text-muted-foreground mt-2 text-center">
                          Pay once, keep forever
                      </p>
                  </TabsContent>
                  <TabsContent value="subscription" className="mt-4">
                      <PayPalSubscriptionButton
                      planName={plan.name}
                        billingCycle={billingCycle}
                          onSuccess={handlePaymentSuccess}
                           onError={handlePaymentError}
                           className={`w-full ${plan.isPopular ? "gradient-button" : ""}`}
                         />
                         <p className="text-xs text-muted-foreground mt-2 text-center">
                           Recurring {billingCycle} billing
                         </p>
                       </TabsContent>
                     </Tabs>
                   ) : user && paypalError ? (
                     <Button 
                       className={`w-full ${plan.isPopular ? "gradient-button" : ""}`}
                       disabled
                     >
                       Payment Unavailable
                     </Button>
                   ) : (
                     <Button 
                       className={`w-full ${plan.isPopular ? "gradient-button" : ""}`}
                       onClick={() => navigate('/login')}
                     >
                       Login to Subscribe
                     </Button>
                   )}

                  <ul className="mt-6 space-y-3">
                    {plan.features.map((feature, i) => (
                      <li key={i} className="flex items-start gap-3">
                        {feature.available ? (
                          <Check className="h-5 w-5 text-primary shrink-0 mt-0.5" />
                        ) : (
                          <X className="h-5 w-5 text-destructive shrink-0 mt-0.5" />
                        )}
                        <span className="flex items-center gap-1">
                          {feature.icon && feature.icon}
                          <span className={feature.available ? "" : "text-muted-foreground line-through"}>
                            {feature.text}
                          </span>
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-8 text-sm text-muted-foreground">
            <p>All prices shown in USD. Taxes may apply depending on your location.</p>
            <p className="mt-2">Need more posts or custom solutions? <Link to="/contact" className="text-primary underline">Contact us</Link> for enterprise pricing.</p>
          </div>
        </div>
      </section>

      {/* FAQs */}
      <section className="section-padding bg-muted/30">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-center mb-12">Frequently Asked Questions</h2>
          <div className="space-y-6">
            {faqs.map((faq, i) => (
              <div key={i} className="bg-card rounded-lg p-6 shadow-sm border">
                <h3 className="text-lg font-semibold mb-2">{faq.question}</h3>
                <p className="text-muted-foreground">{faq.answer}</p>
              </div>
            ))}
          </div>
          <div className="text-center mt-12">
            <p className="text-muted-foreground mb-4">Still have questions?</p>
            <Button variant="outline" size="lg" asChild>
              <Link to="/contact">Contact Support</Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}
