
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";

// Add TypeScript declaration for our window property
declare global {
    interface Window {
        socialSparkLastContent?: {
            CAPTION: string;
            HASHTAGS: string;
            IMAGE_PROMPT: string;
        } | null;
        currentPostId?: string;
    }
}

// Helper to persist content between page refreshes
const saveContentToStorage = (content: any, postId?: string | null) => {
    if (!content) return;

    const key = postId ? `socialSpark_content_${postId}` : 'socialSpark_lastContent';
    localStorage.setItem(key, JSON.stringify(content));
    console.log(`Saved content to storage with key: ${key}`);
};

const getContentFromStorage = (postId?: string | null) => {
    let content = null;

    // First try post-specific content
    if (postId) {
        const storedData = localStorage.getItem(`socialSpark_content_${postId}`);
        if (storedData) {
            try {
                content = JSON.parse(storedData);
                console.log(`Retrieved content from storage for postId: ${postId}`);
                return content;
            } catch (e) {
                console.error('Error parsing stored content:', e);
            }
        }
    }

    // Fall back to last generated content
    const lastContent = localStorage.getItem('socialSpark_lastContent');
    if (lastContent) {
        try {
            content = JSON.parse(lastContent);
            console.log('Retrieved last content from storage');
            return content;
        } catch (e) {
            console.error('Error parsing stored content:', e);
        }
    }

    return null;
};
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
    ArrowLeft, Wand2, Hash, ImageIcon, LayoutGrid, Maximize, Minimize,
    Zap, BarChart2, Sparkles, Settings, Copy, Check, Download, ExternalLink,
    AlertTriangle, Save, Database, RefreshCw
} from "lucide-react";
import { Link, useNavigate, useLocation, useParams } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

export default function Create() {
    const navigate = useNavigate();
    const location = useLocation();
    const { postId } = useParams<{ postId: string }>();
    const { user, profile, loading, checkPostsLimit, incrementPostsCount, refreshProfile } = useAuth();
    const [platform, setPlatform] = useState("Instagram");
    const [customPlatform, setCustomPlatform] = useState("");
    const [tone, setTone] = useState("Professional");
    const [customTone, setCustomTone] = useState("");
    const [topic, setTopic] = useState("");
    const [imageSize, setImageSize] = useState("Auto");
    const [generationQuality, setGenerationQuality] = useState("BALANCED");
    const [hashtagCount, setHashtagCount] = useState(3);
    const [isLoading, setIsLoading] = useState(false);
    const [isSaving, setIsSaving] = useState(false);
    const [isLoadingPost, setIsLoadingPost] = useState(false);
    const [hasRefreshedOnMount, setHasRefreshedOnMount] = useState(false);
    const [generatedContent, setGeneratedContent] = useState<{
        CAPTION: string;
        HASHTAGS: string;
        IMAGE_PROMPT: string;
    } | null>(null);
    const [generatedImage, setGeneratedImage] = useState<string | null>(null);
    const [isGeneratingImage, setIsGeneratingImage] = useState(false);
    const [actualImageAspectRatio, setActualImageAspectRatio] = useState<number>(1);
    const [postSaved, setPostSaved] = useState(false);
    const [savedPostId, setSavedPostId] = useState<string | null>(null);
    const [imageLoadAttempted, setImageLoadAttempted] = useState(false);
    const [hasNavigationState, setHasNavigationState] = useState(false);
    const [copyStatus, setCopyStatus] = useState<{
        caption: boolean;
        hashtags: boolean;
        imagePrompt: boolean;
    }>({
        caption: false,
        hashtags: false,
        imagePrompt: false
    });
    const { toast } = useToast();

    // Check if user is logged in
    useEffect(() => {
        if (!loading && !user) {
            // If we have a postId, include it in the redirect
            const redirectPath = postId ? `/create/${postId}` : "/create";
            navigate("/login", { state: { redirectTo: redirectPath } });
        }
    }, [user, loading, navigate, postId]);

    // Refresh profile data when Create page loads to ensure latest plan info
    useEffect(() => {
        if (user && !loading && !hasRefreshedOnMount) {
            console.log('Create: Refreshing profile to ensure latest plan data');
            setHasRefreshedOnMount(true);
            refreshProfile().then(({ success, error }) => {
                if (success) {
                    console.log('Create: Profile refreshed successfully');
                } else {
                    console.error('Create: Failed to refresh profile:', error);
                }
            });
        }
    }, [user, loading, refreshProfile, hasRefreshedOnMount]);

    // Check if profile needs refresh (if name contains [PLAN UPDATED]) - only once
    useEffect(() => {
        if (profile && profile.full_name && profile.full_name.includes('[PLAN UPDATED]')) {
            console.log('Create: Detected plan update notification, showing toast only');

            // Show toast but don't refresh again to prevent loops
            toast({
                title: "Plan Updated",
                description: "Your subscription plan has been updated",
            });
        }
    }, [profile, toast]);

    // Check if FREE user has Quality mode selected (e.g., after downgrading from paid plan)
    useEffect(() => {
        if (profile && profile.plan === 'free' && generationQuality === 'QUALITY') {
            console.log('FREE user has Quality mode selected, resetting to BALANCED');
            setGenerationQuality('BALANCED');
            toast({
                title: "Quality mode unavailable",
                description: "Quality mode is not available on the FREE plan. Switched to Balanced mode.",
            });
        }
    }, [profile, generationQuality, toast]);

    // Check if the user has reached their posts limit
    const hasReachedPostsLimit = !checkPostsLimit();

    // Log user and profile state
    console.log('Create page - Auth state:', {
        user: user ? 'authenticated' : 'not authenticated',
        profile: profile ? `plan: ${profile.plan}, count: ${profile.posts_count}, limit: ${profile.posts_limit}` : 'no profile',
        loading,
        hasReachedPostsLimit,
        postId,
        generatedContent: generatedContent ? 'present' : 'null',
        generatedImage: generatedImage ? 'present' : 'null',
        postSaved
    });

    // Ensure loading state is cleared after a timeout
    useEffect(() => {
        if (isLoadingPost) {
            const timer = setTimeout(() => {
                console.log('Loading timeout reached, forcing loading state to false');
                setIsLoadingPost(false);
            }, 10000); // 10 seconds timeout

            return () => clearTimeout(timer);
        }
    }, [isLoadingPost]);

    // Add effect to monitor image changes
    useEffect(() => {
        // If we have an image but no content, try to restore from our saved content
        if (generatedImage && (!generatedContent || !generatedContent.CAPTION || generatedContent.CAPTION === 'No caption available')) {
            // First try to get from memory
            const savedContent = window.socialSparkLastContent || getContentFromStorage(postId);

            if (savedContent) {
                console.log('Image loaded but content missing - restoring from saved content');
                setGeneratedContent(savedContent);
            }
        }
    }, [generatedImage, generatedContent, postId]);

    // Fetch post data if postId is provided
    useEffect(() => {
        const fetchPostData = async (retryCount = 0) => {
            if (!postId) return;

            // Add more detailed logging
            console.log('fetchPostData called with:', {
                postId,
                userAuthenticated: !!user,
                retryCount,
                isLoadingPost
            });

            if (!user && retryCount === 0) {
                console.log('User not authenticated yet, will retry after authentication');
                // Schedule a retry after a short delay
                setTimeout(() => fetchPostData(retryCount + 1), 500);
                return; // Don't attempt to fetch if user is not authenticated on first try
            }

            // Reset states to ensure clean loading
            if (retryCount === 0) {
                setIsLoadingPost(true);
            }

            try {
                // Get the auth token
                const authToken = localStorage.getItem("auth_token");
                const headers = authToken ? { Authorization: `Bearer ${authToken}` } : {};

                console.log(`Fetching post data for ID: ${postId} (attempt ${retryCount + 1})`);
                const response = await fetch(`/api/posts/${postId}`, { headers });

                if (!response.ok) {
                    // If we get a 401/403 and we have retry attempts left, we'll retry
                    if ((response.status === 401 || response.status === 403) && retryCount < 3) {
                        console.log(`Auth error (${response.status}), will retry in 1 second...`);
                        setTimeout(() => fetchPostData(retryCount + 1), 1000);
                        return;
                    }
                    throw new Error(`Failed to fetch post: ${response.status}`);
                }

                const postData = await response.json();
                console.log('Fetched post data:', postData);

                // Set form values from post data only if we don't have navigation state
                if (postData.content && !hasNavigationState) {
                    try {
                        // If content is a string, parse it
                        const contentObj = typeof postData.content === 'string'
                            ? JSON.parse(postData.content)
                            : postData.content;

                        console.log('Parsed content object:', contentObj);

                        if (contentObj.platform) {
                            if (['Instagram', 'X', 'LinkedIn', 'Facebook'].includes(contentObj.platform)) {
                                setPlatform(contentObj.platform);
                            } else {
                                setPlatform('Other');
                                setCustomPlatform(contentObj.platform);
                            }
                        }

                        if (contentObj.tone) {
                            const standardTones = [
                                'Professional', 'Casual', 'Enthusiastic', 'Informative',
                                'Humorous', 'Inspirational', 'Formal', 'Friendly'
                            ];

                            if (standardTones.includes(contentObj.tone)) {
                                setTone(contentObj.tone);
                            } else {
                                setTone('Other');
                                setCustomTone(contentObj.tone);
                            }
                        }

                        if (contentObj.topic) {
                            setTopic(contentObj.topic);
                        }

                        if (contentObj.hashtagCount !== undefined) {
                            setHashtagCount(contentObj.hashtagCount);
                        }

                        if (contentObj.imageSize) {
                            setImageSize(contentObj.imageSize);
                        }

                        if (contentObj.quality) {
                            setGenerationQuality(contentObj.quality);
                        }
                    } catch (parseError) {
                        console.error('Error parsing content JSON:', parseError);
                    }
                } else if (hasNavigationState) {
                    console.log('Skipping form value setting from post data due to navigation state priority');
                }

                // Prepare content and image data
                let contentToSet = null;
                let imageToSet = null;

                // Process caption and hashtags
                if (postData.caption) {
                    console.log('Raw caption from server:', postData.caption);

                    // First, check if we have captionPart and hashtagsPart in the content JSON
                    if (postData.content && typeof postData.content === 'object') {
                        const contentObj = postData.content;

                        if (contentObj.captionPart || contentObj.hashtagsPart) {
                            console.log('Found caption and hashtags in content JSON');
                            contentToSet = {
                                CAPTION: contentObj.captionPart || '',
                                HASHTAGS: contentObj.hashtagsPart || '',
                                IMAGE_PROMPT: 'Image prompt not available for saved posts'
                            };

                            // Log to verify we have the data
                            console.log('Using caption/hashtags from content JSON:', {
                                caption: contentToSet.CAPTION.substring(0, 50) + (contentToSet.CAPTION.length > 50 ? '...' : ''),
                                hashtags: contentToSet.HASHTAGS.substring(0, 50) + (contentToSet.HASHTAGS.length > 50 ? '...' : '')
                            });

                            // Store in component state to keep track during image generation
                            window.socialSparkLastContent = contentToSet;

                            // Store in component state to keep track during image generation
                            window.socialSparkLastContent = contentToSet;
                        } else {
                            // Fall back to splitting caption by newlines
                            console.log('No caption/hashtags in content JSON, using splitting method');
                            splitCaptionAndHashtags();
                        }
                    } else {
                        // Fall back to splitting caption by newlines
                        console.log('Using splitting method to extract caption and hashtags');
                        splitCaptionAndHashtags();
                    }

                    // Helper function to split caption and hashtags
                    function splitCaptionAndHashtags() {
                        // Split caption and hashtags
                        const parts = postData.caption.split('\n\n');
                        console.log('Caption parts after splitting:', parts);

                        if (parts.length >= 2) {
                            const caption = parts[0];
                            const hashtags = parts[1];

                            console.log('Setting caption and hashtags:', {
                                caption: caption.substring(0, 50) + (caption.length > 50 ? '...' : ''),
                                hashtags: hashtags.substring(0, 50) + (hashtags.length > 50 ? '...' : '')
                            });

                            // Create generatedContent object
                            contentToSet = {
                                CAPTION: caption,
                                HASHTAGS: hashtags,
                                IMAGE_PROMPT: 'Image prompt not available for saved posts'
                            };
                        } else {
                            console.log('Setting caption only (no hashtags found):',
                                postData.caption.substring(0, 50) + (postData.caption.length > 50 ? '...' : ''));

                            // If there's no clear separation, assume it's all caption
                            contentToSet = {
                                CAPTION: postData.caption,
                                HASHTAGS: '',
                                IMAGE_PROMPT: 'Image prompt not available for saved posts'
                            };
                        }
                    }
                } else {
                    console.warn('No caption found in post data');
                    // Create a minimal content object to ensure rendering
                    contentToSet = {
                        CAPTION: 'No caption available',
                        HASHTAGS: 'Loading hashtags...',
                        IMAGE_PROMPT: 'No image prompt available'
                    };

                    console.log('No caption found - checking content JSON for caption/hashtags data');
                    // Check if there's caption/hashtag data in the content JSON even if caption field is empty
                    if (postData.content && typeof postData.content === 'object') {
                        const contentObj = postData.content;
                        if (contentObj.captionPart || contentObj.hashtagsPart) {
                            console.log('Found caption/hashtags in content JSON despite missing caption field');
                            contentToSet = {
                                CAPTION: contentObj.captionPart || 'No caption available',
                                HASHTAGS: contentObj.hashtagsPart || 'No hashtags available',
                                IMAGE_PROMPT: 'Image prompt not available for saved posts'
                            };

                            // Store for future reference
                            window.socialSparkLastContent = contentToSet;
                        }
                    }

                    // Try to fetch the post again to get the caption
                    setTimeout(() => {
                        const authToken = localStorage.getItem("auth_token");
                        if (authToken) {
                            fetch(`/api/posts/${postId}`, {
                                headers: { Authorization: `Bearer ${authToken}` }
                            })
                                .then(response => response.json())
                                .then(refreshedData => {
                                    if (refreshedData.caption) {
                                        console.log('Successfully retrieved caption on retry');
                                        const parts = refreshedData.caption.split('\n\n');
                                        if (parts.length >= 2) {
                                            setGeneratedContent({
                                                CAPTION: parts[0],
                                                HASHTAGS: parts[1],
                                                IMAGE_PROMPT: 'Image prompt not available for saved posts'
                                            });
                                        } else {
                                            setGeneratedContent({
                                                CAPTION: refreshedData.caption,
                                                HASHTAGS: '',
                                                IMAGE_PROMPT: 'Image prompt not available for saved posts'
                                            });
                                        }
                                    }
                                })
                                .catch(error => {
                                    console.error('Error in caption recovery attempt:', error);
                                });
                        }
                    }, 1000);
                }

                // Log the content that will be set
                console.log('Content object to be set:', {
                    CAPTION: contentToSet.CAPTION ?
                        (contentToSet.CAPTION.substring(0, 50) + (contentToSet.CAPTION.length > 50 ? '...' : '')) : 'empty',
                    HASHTAGS: contentToSet.HASHTAGS ?
                        (contentToSet.HASHTAGS.substring(0, 50) + (contentToSet.HASHTAGS.length > 50 ? '...' : '')) : 'empty'
                });

                // Process image data
                if (postData.imageData) {
                    console.log('Setting image data from fetched post');
                    imageToSet = postData.imageData;
                } else if (postData.imageUrl) {
                    console.log('Image URL found but no image data, attempting to fetch image:', postData.imageUrl);
                    // Mark that we attempted to load an image
                    setImageLoadAttempted(true);

                    try {
                        // Try to fetch the image directly
                        const imageResponse = await fetch(postData.imageUrl, { headers });
                        if (imageResponse.ok) {
                            const blob = await imageResponse.blob();
                            const reader = new FileReader();

                            // Use a Promise to handle the async FileReader
                            imageToSet = await new Promise((resolve) => {
                                reader.onloadend = () => {
                                    const base64data = reader.result as string;
                                    console.log('Successfully loaded image from URL');
                                    resolve(base64data);
                                };
                                reader.readAsDataURL(blob);
                            });
                        } else {
                            console.error('Failed to fetch image from URL:', postData.imageUrl);
                        }
                    } catch (imgError) {
                        console.error('Error fetching image from URL:', imgError);
                    }
                } else {
                    console.warn('No image data or URL found in post data');
                }

                // If the server indicates an image was attempted but we don't have it
                if (postData.imageLoadAttempted && !imageToSet) {
                    console.log('Server indicates image was attempted but failed to load');
                    setImageLoadAttempted(true);
                }

                // Try to get content from localStorage first
                const savedContent = getContentFromStorage(postId);
                if (savedContent && (!contentToSet || contentToSet.CAPTION === 'No caption available')) {
                    console.log('Found saved content in localStorage for this post');
                    contentToSet = savedContent;
                }

                // Update all state at once to ensure consistency
                console.log('Updating state with fetched data:', {
                    hasContent: !!contentToSet,
                    hasImage: !!imageToSet,
                    usedLocalStorage: !!savedContent
                });

                // Create a local copy to ensure we're not affected by any reference issues
                const contentCopy = contentToSet ? {
                    CAPTION: contentToSet.CAPTION || '',
                    HASHTAGS: contentToSet.HASHTAGS || '',
                    IMAGE_PROMPT: contentToSet.IMAGE_PROMPT || 'Image prompt not available for saved posts'
                } : null;

                // Set all states at once
                if (contentCopy) {
                    console.log('Setting generatedContent with copy:', {
                        CAPTION: contentCopy.CAPTION.substring(0, 50) + (contentCopy.CAPTION.length > 50 ? '...' : ''),
                        HASHTAGS: contentCopy.HASHTAGS.substring(0, 50) + (contentCopy.HASHTAGS.length > 50 ? '...' : '')
                    });
                    setGeneratedContent(contentCopy);

                    // Store in localStorage for persistence
                    saveContentToStorage(contentCopy, postId);
                }

                if (imageToSet) setGeneratedImage(imageToSet);
                setPostSaved(true);
                setSavedPostId(postId);

                // Force a re-render by setting a timeout
                setTimeout(() => {
                    console.log('Forced re-render check - content and image state:', {
                        content: !!generatedContent,
                        contentCaptionLength: generatedContent ? generatedContent.CAPTION.length : 0,
                        contentHashtagsLength: generatedContent ? generatedContent.HASHTAGS.length : 0,
                        image: !!generatedImage
                    });
                }, 100);

                console.log('Post data successfully loaded and state updated', {
                    contentSet: !!generatedContent,
                    imageSet: !!generatedImage,
                    postSaved,
                    savedPostId
                });

                // Debug: Log the actual content of generatedContent
                if (generatedContent) {
                    console.log('Generated content after loading:', {
                        caption: generatedContent.CAPTION ? generatedContent.CAPTION.substring(0, 50) + '...' : 'empty',
                        hashtags: generatedContent.HASHTAGS ? generatedContent.HASHTAGS.substring(0, 50) + '...' : 'empty',
                        imagePrompt: generatedContent.IMAGE_PROMPT ? 'present' : 'empty'
                    });
                } else {
                    console.log('WARNING: generatedContent is null after setting it');
                }

            } catch (error) {
                console.error('Error fetching post:', error);

                // If we have retry attempts left, retry after a delay
                if (retryCount < 3) {
                    console.log(`Fetch failed, will retry in ${(retryCount + 1) * 1000}ms...`);
                    setTimeout(() => fetchPostData(retryCount + 1), (retryCount + 1) * 1000);
                    return;
                }

                toast({
                    title: "Error loading post",
                    description: error instanceof Error ? error.message : "Failed to load post data",
                    variant: "destructive"
                });
            } finally {
                setIsLoadingPost(false);
                console.log('Finished loading post, isLoadingPost set to false');
            }
        };

        // Start fetching post data
        if (postId) {
            console.log('PostId detected in URL, initiating data fetch:', postId);
            fetchPostData();
        }
    }, [postId, toast, user]);

    // Calculate actual aspect ratio from loaded image
    useEffect(() => {
        if (generatedImage) {
            const img = new Image();
            img.onload = () => {
                const ratio = img.width / img.height;
                setActualImageAspectRatio(ratio);
            };
            img.src = generatedImage;
        }
    }, [generatedImage]);

    // Check if we have state from the home page - prioritize this over existing post data
    useEffect(() => {
        if (location.state) {
            const { generatedContent, platform: statePlatform, tone: stateTone, topic: stateTopic } = location.state as any;

            // If we have navigation state, prioritize it over existing post data
            if (statePlatform || stateTone || stateTopic) {
                console.log('Setting form values from navigation state:', { statePlatform, stateTone, stateTopic });
                setHasNavigationState(true); // Mark that we have navigation state
                if (statePlatform) setPlatform(statePlatform);
                if (stateTone) setTone(stateTone);
                if (stateTopic) setTopic(stateTopic);

                // Clear any existing content when coming from home page with new form data
                if (!generatedContent) {
                    console.log('Clearing existing content for new form data from home page');
                    setGeneratedContent(null);
                    setGeneratedImage(null);
                }
            }

            if (generatedContent) {
                console.log('Setting content from location state');
                setGeneratedContent(generatedContent);
                // Only generate image if we don't already have one
                if (!generatedImage && generatedContent.IMAGE_PROMPT) {
                    // Store content first to make sure we don't lose it during image generation
                    const contentBackup = generatedContent;
                    setTimeout(() => {
                        // Make sure we have the content stored before starting image generation
                        if (!generatedContent && contentBackup) {
                            console.log('Restoring content before image generation');
                            setGeneratedContent(contentBackup);
                        }
                        generateImage(contentBackup.IMAGE_PROMPT);
                    }, 100);
                }
            }
        }
    }, [location.state, generatedImage]);

    // Monitor state changes for debugging
    useEffect(() => {
        console.log('State change detected:', {
            hasContent: !!generatedContent,
            hasImage: !!generatedImage,
            postSaved,
            isLoadingPost,
            isGeneratingImage,
            imageLoadAttempted,
            postId
        });

        // Debug log for captions and hashtags
        if (generatedContent) {
            console.log('Current content state:', {
                caption: generatedContent.CAPTION ? generatedContent.CAPTION.substring(0, 30) + '...' : 'none',
                hashtags: generatedContent.HASHTAGS ? generatedContent.HASHTAGS.substring(0, 30) + '...' : 'none'
            });

            // Save content to localStorage for persistence
            saveContentToStorage(generatedContent, postId);
            window.socialSparkLastContent = { ...generatedContent };
        }

        // Check if we came from gallery view, which means we want to prioritize image loading
        const comingFromGallery = location.state && location.state.from === 'gallery' && location.state.loadImage;

        // If we have content but no image, and we're not loading, try to load the image
        if ((generatedContent && !generatedImage && !isLoadingPost && !isGeneratingImage && postId) || (comingFromGallery && postId)) {
            console.log('Content loaded but no image, attempting to reload post data');
            console.log('Coming from gallery:', comingFromGallery || false);

            // This could happen if the image loading failed the first time
            const authToken = localStorage.getItem("auth_token");
            if (authToken) {
                fetch(`/api/posts/${postId}`, {
                    headers: { Authorization: `Bearer ${authToken}` }
                })
                    .then(response => {
                        if (response.ok) return response.json();
                        throw new Error(`Failed to fetch post: ${response.status}`);
                    })
                    .then(postData => {
                        if (postData.imageData) {
                            console.log('Successfully retrieved image data on second attempt');
                            setGeneratedImage(postData.imageData);
                        }

                        // If we're missing content but have caption and hashtags in content, set them
                        if ((!generatedContent || !generatedContent.CAPTION) && postData.content &&
                            (postData.content.captionPart || postData.content.hashtagsPart)) {
                            console.log('Recovering caption and hashtags from content object');
                            const recoveredContent = {
                                CAPTION: postData.content.captionPart || '',
                                HASHTAGS: postData.content.hashtagsPart || '',
                                IMAGE_PROMPT: 'Image prompt not available for saved posts'
                            };
                            setGeneratedContent(recoveredContent);
                            saveContentToStorage(recoveredContent, postId);
                        } else {
                            // Try to get from localStorage if we don't have content in the API response
                            const savedContent = getContentFromStorage(postId);
                            if (savedContent && (!generatedContent || !generatedContent.CAPTION || generatedContent.CAPTION === 'No caption available')) {
                                console.log('Recovering content from localStorage for this post');
                                setGeneratedContent(savedContent);
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error in image recovery attempt:', error);
                    });
            }
        }
    }, [generatedContent, generatedImage, postSaved, isLoadingPost, isGeneratingImage, postId]);

    // Centralized function to generate an image from a prompt
    const generateImage = async (imagePrompt: string) => {
        if (!imagePrompt) return;

        // Save current content before image generation to ensure we can restore it
        if (generatedContent) {
            console.log('Saving content before image generation');
            window.socialSparkLastContent = { ...generatedContent };
        }

        setIsGeneratingImage(true);
        // Don't set generatedImage to null to prevent wiping out caption/hashtags display
        // Only set to null if we actually need to refresh the image
        if (generatedImage) {
            setGeneratedImage(null);
        }

        try {
            // Prepare post content for auto-saving
            const actualPlatform = platform === "Other" ? customPlatform : platform;
            const actualTone = tone === "Other" ? customTone : tone;

            // Save current content to ensure we don't lose it during image generation
            if (generatedContent) {
                console.log('Saving content before API call');
                window.socialSparkLastContent = { ...generatedContent };
                saveContentToStorage(generatedContent, savedPostId);
            } else {
                // Try to restore from localStorage if we don't have content
                const savedContent = getContentFromStorage(savedPostId);
                if (savedContent) {
                    console.log('Restored content from localStorage before image generation');
                    setGeneratedContent(savedContent);
                    window.socialSparkLastContent = { ...savedContent };
                }
            }

            // Create post content JSON
            const postContent = JSON.stringify({
                platform: actualPlatform,
                tone: actualTone,
                topic,
                hashtagCount,
                imageSize,
                quality: generationQuality,
                postId: savedPostId, // Include the post ID if we have one already
                // Include caption and hashtags in the content JSON as well
                captionPart: generatedContent?.CAPTION || window.socialSparkLastContent?.CAPTION || '',
                hashtagsPart: generatedContent?.HASHTAGS || window.socialSparkLastContent?.HASHTAGS || ''
            });

            // Get caption and hashtags for auto-saving
            let caption = null;
            const captionPart = generatedContent ? (generatedContent.CAPTION || '') : '';
            const hashtagsPart = generatedContent ? (generatedContent.HASHTAGS || '') : '';

            // Format the caption with a clear separator
            if (captionPart || hashtagsPart) {
                if (captionPart && hashtagsPart) {
                    caption = `${captionPart}\n\n${hashtagsPart}`;
                } else {
                    caption = captionPart || hashtagsPart;
                }
            }

            // Ensure caption is not null or undefined
            caption = caption || '';

            // Log the caption components for debugging
            console.log('Image generation caption components:', {
                captionPart: captionPart.substring(0, 50) + (captionPart.length > 50 ? '...' : ''),
                hashtagsPart: hashtagsPart.substring(0, 50) + (hashtagsPart.length > 50 ? '...' : '')
            });

            console.log('Image generation with caption:', caption ? caption.substring(0, 100) + '...' : 'null');
            console.log('Image generation with savedPostId:', savedPostId || 'none');

            // Get auth token for user identification
            const authToken = localStorage.getItem('auth_token');

            // Make sure we use the existing post ID if we have one
            const existingPostId = savedPostId || postId;
            console.log('Using post ID for image generation:', existingPostId || 'none');

            // Log all our state for debugging
            console.log('Current state before image generation:', {
                savedPostId,
                postId,
                existingPostId,
                hasGeneratedContent: !!generatedContent,
                hasImage: !!generatedImage,
                pathname: window.location.pathname
            });

            // Make sure caption and hashtags are included
            const contentToSend = {
                ...JSON.parse(postContent),
                captionPart: captionPart || generatedContent?.CAPTION || window.socialSparkLastContent?.CAPTION || '',
                hashtagsPart: hashtagsPart || generatedContent?.HASHTAGS || window.socialSparkLastContent?.HASHTAGS || ''
            };

            console.log('Sending content to image generation API:', {
                hasExistingPostId: !!existingPostId,
                hasCaption: !!contentToSend.captionPart,
                hasHashtags: !!contentToSend.hashtagsPart
            });

            const response = await fetch('/api/generate-image', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': authToken ? `Bearer ${authToken}` : ''
                },
                body: JSON.stringify({
                    imagePrompt,
                    postContent: JSON.stringify(contentToSend),
                    caption,
                    // Always include the savedPostId if we have one to prevent creating duplicate posts
                    postId: existingPostId || null,
                    userId: authToken // Send token for verification on server
                }),
            });

            const data = await response.json();

            if (!response.ok) {
                console.error('Image generation API error:', data);

                // Handle post limit error specifically
                if (data.limitReached) {
                    toast({
                        title: "Posts limit reached",
                        description: data.error || "You've reached your monthly posts limit. Upgrade your plan for more posts.",
                        variant: "destructive"
                    });
                    navigate("/pricing");
                    return;
                }

                // Handle quality restriction error specifically
                if (data.qualityRestriction) {
                    toast({
                        title: "Quality mode unavailable",
                        description: data.error || "Quality mode is not available on the FREE plan. Please upgrade to use this feature.",
                        variant: "destructive"
                    });
                    setGenerationQuality("BALANCED");
                    navigate("/pricing");
                    return;
                }

                // Handle all API keys failed error specifically
                if (data.allKeysFailed) {
                    toast({
                        title: "Service temporarily unavailable",
                        description: data.error || "All API keys have failed or reached their limits. Please try again later.",
                        variant: "destructive"
                    });

                    // Restore the user's original content if available
                    if (data.originalContent) {
                        console.log('Restoring original content after API key failure:', data.originalContent);
                        // You can restore form values here if needed
                    }
                    return;
                }

                throw new Error(data.error || `API error: ${response.status}`);
            }

            // If the server auto-saved the post during image generation
            if (data.postResult && data.postResult.success) {
                // Get post ID (either existing or new)
                const resultPostId = data.postResult.postId;
                console.log('Server saved post with ID:', resultPostId);

                // See if the post ID from the server matches our current post ID
                const currentPath = window.location.pathname;
                const currentPathPostId = currentPath.split('/').pop();

                console.log('Comparing post IDs:', {
                    fromServer: resultPostId,
                    fromCurrentPath: currentPathPostId,
                    savedPostId,
                    postId,
                    doPathAndServerMatch: currentPathPostId === resultPostId
                });

                // CRITICAL FIX: Only change URL if we don't have a post ID in our path yet
                if (!currentPath.includes('/create/')) {
                    console.log('No post ID in URL yet, adding it');
                    navigate(`/create/${resultPostId}`, { replace: true });
                } else if (currentPathPostId && currentPathPostId !== resultPostId) {
                    // If there's a mismatch between server and URL, we have a bigger problem,
                    // but we'll prioritize keeping the URL stable to avoid duplicate posts
                    console.log('WARNING: Post ID mismatch between server and URL');
                    // DON'T change the URL here to prevent the duplicate post issue
                }

                // Update state regardless
                setPostSaved(true);
                setSavedPostId(resultPostId);

                toast({
                    title: "Post saved",
                    description: "Your post has been saved with the generated image"
                });
            } else if (data.postResult && data.postResult.error) {
                console.error('Error auto-saving post:', data.postResult.error);
            }

            // Compress the image before storing it in state
            try {
                const compressedImageData = await compressImage(data.imageData);

                // Save the current content to a variable before updating the image
                const savedContent = generatedContent || window.socialSparkLastContent || getContentFromStorage(savedPostId || postId);

                // Always save the content before setting the image
                if (savedContent) {
                    saveContentToStorage(savedContent, data.postResult?.postId || savedPostId || postId);
                }

                // Set the image
                setGeneratedImage(compressedImageData);

                // Restore content after a short delay to ensure UI updates properly
                setTimeout(() => {
                    if ((!generatedContent || Object.keys(generatedContent).length === 0 || generatedContent.CAPTION === 'No caption available') && savedContent) {
                        console.log('Restoring content after image generation', savedContent);
                        setGeneratedContent(savedContent);
                    }
                }, 100);

                console.log('Image compressed before storing in state');
            } catch (err) {
                console.error('Error compressing generated image:', err);
                // Fall back to original image if compression fails
                setGeneratedImage(data.imageData);

                // Also restore content here in case of error
                const savedContent = window.socialSparkLastContent;
                if (savedContent && (!generatedContent || Object.keys(generatedContent).length === 0)) {
                    setTimeout(() => setGeneratedContent(savedContent), 100);
                }
            }

            toast({
                title: "Image generated",
                description: "Your custom image has been created!"
            });

            // Final check after toast to make sure content is preserved
            setTimeout(() => {
                // If content is missing after all our attempts, use our saved content
                if (!generatedContent && window.socialSparkLastContent) {
                    console.log('Final restoration of content after image generation');
                    setGeneratedContent(window.socialSparkLastContent);
                }
            }, 500);
        } catch (error) {
            console.error('Error generating image:', error);

            // Show a more user-friendly error message
            let errorMessage = "An unknown error occurred";
            if (error instanceof Error) {
                errorMessage = error.message;

                // Make the error message more user-friendly
                if (errorMessage.includes("INVALID_ARGUMENT")) {
                    errorMessage = "The image prompt is not compatible with the image generation model. Try a simpler prompt.";
                } else if (errorMessage.includes("PERMISSION_DENIED")) {
                    errorMessage = "API key doesn't have permission to generate images.";
                } else if (errorMessage.includes("RESOURCE_EXHAUSTED")) {
                    errorMessage = "API quota exceeded. Please try again later.";
                }
            }

            toast({
                title: "Image generation failed",
                description: errorMessage,
                variant: "destructive"
            });
        } finally {
            setIsGeneratingImage(false);
        }
    };

    // Function to fetch and log recent posts count
    const logRecentPostsCount = async () => {
        try {
            const response = await fetch('/api/posts/recent-count?minutes=1', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                console.log(`📊 Posts generated in the last minute: ${data.count}`);
                console.log(`📈 Timeframe: ${data.timeframe}`);
                return data.count;
            } else {
                console.warn('Failed to fetch recent posts count');
                return 0;
            }
        } catch (error) {
            console.error('Error fetching recent posts count:', error);
            return 0;
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        // Log recent posts count when Generate Custom Content is clicked
        await logRecentPostsCount();

        // FIRST: Check rate limit (posts per minute) before doing anything else
        try {
            const rateLimitResponse = await fetch('/api/check-rate-limit', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
                }
            });

            if (rateLimitResponse.status === 429) {
                const rateLimitData = await rateLimitResponse.json();
                toast({
                    title: "Rate limit exceeded",
                    description: rateLimitData.error,
                    variant: "destructive"
                });
                return; // Stop here, don't generate anything
            } else if (!rateLimitResponse.ok) {
                console.warn('Rate limit check failed, continuing anyway');
            }
        } catch (rateLimitError) {
            console.error('Error checking rate limit:', rateLimitError);
            // Continue anyway if rate limit check fails
        }

        // Refresh profile data before generating to ensure latest plan limits (only if not recently refreshed)
        if (!hasReachedPostsLimit) {
            console.log('Create: Refreshing profile before generating post');
            const { success } = await refreshProfile();
            if (!success) {
                console.warn('Create: Profile refresh failed before generation, continuing anyway');
            }
        }

        // Check if user has reached posts limit (after refresh)
        if (hasReachedPostsLimit) {
            toast({
                title: "Posts limit reached",
                description: "You've reached your monthly posts limit. Upgrade your plan for more posts.",
                variant: "destructive"
            });
            navigate("/pricing");
            return;
        }

        setIsLoading(true);
        setGeneratedContent(null);
        setGeneratedImage(null);
        setPostSaved(false);

        // First, create a placeholder post to get a post ID and redirect immediately
        try {
            // Create a placeholder post content
            const actualPlatform = platform === "Other" ? customPlatform : platform;
            const actualTone = tone === "Other" ? customTone : tone;

            const placeholderContent = JSON.stringify({
                platform: actualPlatform,
                tone: actualTone,
                topic,
                hashtagCount,
                imageSize,
                quality: generationQuality,
                captionPart: "Generating...",
                hashtagsPart: "Generating...",
                isGenerating: true
            });

            console.log('Creating placeholder post to get ID...');

            // Save placeholder to server to get a post ID
            const placeholderResponse = await fetch('/api/posts', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
                },
                body: JSON.stringify({
                    content: placeholderContent,
                    caption: "Generating content...",
                    imageUrl: null
                })
            });

            if (!placeholderResponse.ok) {
                const errorData = await placeholderResponse.json();

                // Handle post limit error specifically
                if (errorData.limitReached) {
                    toast({
                        title: "Posts limit reached",
                        description: errorData.error || "You've reached your monthly posts limit. Upgrade your plan for more posts.",
                        variant: "destructive"
                    });
                    navigate("/pricing");
                    return;
                }

                // Handle quality restriction error specifically
                if (errorData.qualityRestriction) {
                    toast({
                        title: "Quality mode unavailable",
                        description: errorData.error || "Quality mode is not available on the FREE plan. Please upgrade to use this feature.",
                        variant: "destructive"
                    });
                    setGenerationQuality("BALANCED");
                    navigate("/pricing");
                    return;
                }

                throw new Error(errorData.error || `API error: ${placeholderResponse.status}`);
            }

            const placeholderData = await placeholderResponse.json();

            // Set the post as saved with the new ID
            setPostSaved(true);
            setSavedPostId(placeholderData.postId);

            // Redirect to the post URL immediately
            if (!postId) {
                navigate(`/create/${placeholderData.postId}`, { replace: true });
            }

            // Now generate the actual content
            try {
                // Map quality setting to API values
                const qualityMapping = {
                    "FAST": "fast",
                    "BALANCED": "balanced",
                    "QUALITY": "quality"
                };

                // Make sure we pass the existing post ID if we already have one
                console.log('Generating content with post ID:', placeholderData.postId);

                const response = await fetch('/api/generate-post', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
                    },
                    body: JSON.stringify({
                        platform: actualPlatform,
                        tone: actualTone,
                        topic,
                        hashtagCount,
                        imageSize,
                        quality: qualityMapping[generationQuality as keyof typeof qualityMapping],
                        // Pass the existing post ID to ensure we update the same post
                        postId: placeholderData.postId
                    }),
                });

                if (!response.ok) {
                    const errorData = await response.json();

                    // Handle post limit error specifically
                    if (errorData.limitReached) {
                        toast({
                            title: "Posts limit reached",
                            description: errorData.error || "You've reached your monthly posts limit. Upgrade your plan for more posts.",
                            variant: "destructive"
                        });
                        navigate("/pricing");
                        return;
                    }

                    // Handle quality restriction error specifically
                    if (errorData.qualityRestriction) {
                        toast({
                            title: "Quality mode unavailable",
                            description: errorData.error || "Quality mode is not available on the FREE plan. Please upgrade to use this feature.",
                            variant: "destructive"
                        });
                        setGenerationQuality("BALANCED");
                        navigate("/pricing");
                        return;
                    }

                    // Handle all API keys failed error specifically
                    if (errorData.allKeysFailed) {
                        toast({
                            title: "Service temporarily unavailable",
                            description: errorData.error || "All API keys have failed or reached their limits. Please try again later.",
                            variant: "destructive"
                        });

                        // Restore the user's original content if available
                        if (errorData.originalContent) {
                            console.log('Restoring original content after API key failure:', errorData.originalContent);
                            // Restore form values
                            const original = errorData.originalContent;
                            if (original.platform) setPlatform(original.platform);
                            if (original.tone) setTone(original.tone);
                            if (original.topic) setTopic(original.topic);
                            if (original.hashtagCount !== undefined) setHashtagCount(original.hashtagCount);
                            if (original.imageSize) setImageSize(original.imageSize);
                        }
                        return;
                    }

                    throw new Error(errorData.error || `API error: ${response.status}`);
                }

                const data = await response.json();
                setGeneratedContent(data);

                // Note: Post count will be incremented automatically when the post is saved to database
                // No need to increment here as it would cause double counting

                // Update the post with the generated content
                try {
                    // Format caption for saving
                    const caption = data.CAPTION || '';
                    const hashtags = data.HASHTAGS || '';
                    let formattedCaption = '';

                    if (caption) {
                        formattedCaption = hashtags ? `${caption}\n\n${hashtags}` : caption;
                    } else if (hashtags) {
                        formattedCaption = hashtags;
                    }

                    // Create post content JSON
                    const postContent = JSON.stringify({
                        platform: actualPlatform,
                        tone: actualTone,
                        topic,
                        hashtagCount,
                        imageSize,
                        quality: generationQuality,
                        captionPart: caption,
                        hashtagsPart: hashtags,
                        isGenerating: false
                    });

                    console.log('Updating post with generated content...', placeholderData.postId);

                    // Save a copy to localStorage for persistence
                    saveContentToStorage({
                        CAPTION: caption,
                        HASHTAGS: hashtags,
                        IMAGE_PROMPT: data.IMAGE_PROMPT || ''
                    }, placeholderData.postId);

                    // Update the post with the generated content
                    const updateResponse = await fetch('/api/posts', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
                        },
                        body: JSON.stringify({
                            content: postContent,
                            caption: formattedCaption,
                            imageUrl: null, // No image yet
                            postId: placeholderData.postId // Use the same post ID
                        })
                    });

                    if (updateResponse.ok) {
                        toast({
                            title: "Content generated",
                            description: "Your content has been generated and saved"
                        });
                    }
                } catch (updateError) {
                    console.error('Error updating post with generated content:', updateError);
                }

                // Automatically generate the image only if user hasn't reached post limit
                if (data.IMAGE_PROMPT && !hasReachedPostsLimit) {
                    // Update our state so we know which post ID to use
                    setSavedPostId(placeholderData.postId);
                    setPostSaved(true);

                    // A short delay to make sure state is updated
                    setTimeout(() => {
                        console.log('Generating image for post:', placeholderData.postId);
                        generateImage(data.IMAGE_PROMPT);
                    }, 100);
                } else if (hasReachedPostsLimit) {
                    toast({
                        title: "Image generation skipped",
                        description: "Image generation was skipped because you've reached your monthly posts limit.",
                        variant: "destructive"
                    });
                }
            } catch (genError) {
                console.error('Error generating content:', genError);
                toast({
                    title: "Generation failed",
                    description: genError instanceof Error ? genError.message : "An unknown error occurred",
                    variant: "destructive"
                });
            }
        } catch (error) {
            console.error('Error creating placeholder post:', error);
            toast({
                title: "Post creation failed",
                description: error instanceof Error ? error.message : "An unknown error occurred",
                variant: "destructive"
            });
        } finally {
            setIsLoading(false);
        }
    };

    const handleCopy = async (type: 'caption' | 'hashtags' | 'imagePrompt') => {
        if (!generatedContent) return;

        // Map the type to the corresponding property in generatedContent
        const contentMap = {
            caption: 'CAPTION',
            hashtags: 'HASHTAGS',
            imagePrompt: 'IMAGE_PROMPT'
        };

        const contentKey = contentMap[type];

        try {
            await navigator.clipboard.writeText(generatedContent[contentKey as keyof typeof generatedContent]);

            // Set copy status for this type to true
            setCopyStatus(prev => ({ ...prev, [type]: true }));

            // Reset copy status after 2 seconds
            setTimeout(() => {
                setCopyStatus(prev => ({ ...prev, [type]: false }));
            }, 2000);

            toast({
                title: "Copied to clipboard",
                description: `${type.charAt(0).toUpperCase() + type.slice(1)} copied to clipboard`,
            });
        } catch (error) {
            toast({
                title: "Copy failed",
                description: "Failed to copy to clipboard",
                variant: "destructive"
            });
        }
    };

    const handleDownloadImage = () => {
        if (!generatedImage) return;

        // Create a temporary anchor element
        const link = document.createElement('a');
        link.href = generatedImage;
        link.download = `social-spark-image-${new Date().getTime()}.png`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        toast({
            title: "Image downloaded",
            description: "Your generated image has been downloaded"
        });
    };

    const handlePreviewImage = () => {
        if (!generatedImage || !generatedContent) {
            toast({
                title: "Cannot preview",
                description: "Both image and content must be generated first",
                variant: "destructive"
            });
            return;
        }

        try {
            // Create a data object with all the necessary information
            const previewData = {
                platform: platform === "Other" ? customPlatform : platform,
                caption: generatedContent.CAPTION || '',
                hashtags: generatedContent.HASHTAGS || '',
                imageData: generatedImage,
                imageSize: imageSize
            };

            console.log('Preparing preview data:', {
                platform: previewData.platform,
                captionLength: previewData.caption ? previewData.caption.length : 0,
                hashtagsLength: previewData.hashtags ? previewData.hashtags.length : 0,
                hasImageData: !!previewData.imageData,
                imageSize: previewData.imageSize
            });

            // Store the data in localStorage instead of passing it in the URL
            // This avoids URL length limitations
            const previewId = `preview_${Date.now()}`;
            localStorage.setItem(previewId, JSON.stringify(previewData));

            // Navigate to the preview page with just the ID
            navigate(`/preview?id=${previewId}`);

            console.log("Preview data stored with ID:", previewId);
        } catch (error) {
            console.error("Error preparing preview data:", error);
            toast({
                title: "Preview failed",
                description: "There was an error preparing the preview data",
                variant: "destructive"
            });
        }
    };

    // Helper function to compress image
    const compressImage = async (dataUrl: string): Promise<string> => {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => {
                // Create a canvas with reduced dimensions
                const canvas = document.createElement('canvas');
                // Limit max dimension to 1200px while maintaining aspect ratio
                const maxDimension = 1200;
                let width = img.width;
                let height = img.height;

                if (width > height && width > maxDimension) {
                    height = (height * maxDimension) / width;
                    width = maxDimension;
                } else if (height > maxDimension) {
                    width = (width * maxDimension) / height;
                    height = maxDimension;
                }

                canvas.width = width;
                canvas.height = height;

                // Draw image on canvas with reduced quality
                const ctx = canvas.getContext('2d');
                if (!ctx) {
                    reject(new Error('Could not get canvas context'));
                    return;
                }

                ctx.drawImage(img, 0, 0, width, height);

                // Convert to compressed JPEG with 0.7 quality
                const compressedDataUrl = canvas.toDataURL('image/jpeg', 0.7);
                resolve(compressedDataUrl);
            };

            img.onerror = () => {
                reject(new Error('Failed to load image for compression'));
            };

            img.src = dataUrl;
        });
    };

    const savePost = async () => {
        if (!generatedContent || !user) {
            toast({
                title: "Cannot save post",
                description: "Please generate content first and make sure you're logged in",
                variant: "destructive"
            });
            return;
        }

        setIsSaving(true);

        try {
            // Prepare the post data
            const actualPlatform = platform === "Other" ? customPlatform : platform;

            // Compress the image if it exists
            let compressedImageUrl = null;
            if (generatedImage) {
                try {
                    compressedImageUrl = await compressImage(generatedImage);
                    console.log('Image compressed successfully');
                } catch (err) {
                    console.error('Error compressing image:', err);
                    // Fall back to original image if compression fails
                    compressedImageUrl = generatedImage;
                }
            }

            // Get caption and hashtags separately
            const caption = generatedContent.CAPTION || '';
            const hashtags = generatedContent.HASHTAGS || '';

            // Format the caption with a clear separator between caption and hashtags
            // This ensures they're stored together in the database but can be separated in the UI
            let formattedCaption = '';

            if (caption) {
                formattedCaption = hashtags.trim() ?
                    `${caption}\n\n${hashtags}` :
                    caption;
            } else if (hashtags) {
                formattedCaption = hashtags;
            }

            // Ensure we have at least an empty string
            formattedCaption = formattedCaption || '';

            console.log('Saving post with formatted caption:', formattedCaption.substring(0, 100) + '...');
            console.log('Caption and hashtags parts:', {
                caption: caption.substring(0, 50) + (caption.length > 50 ? '...' : ''),
                hashtags: hashtags.substring(0, 50) + (hashtags.length > 50 ? '...' : '')
            });

            const postData = {
                content: JSON.stringify({
                    platform: actualPlatform,
                    tone: tone === "Other" ? customTone : tone,
                    topic,
                    hashtagCount,
                    imageSize,
                    quality: generationQuality,
                    // Store caption and hashtags separately in the content JSON as well
                    captionPart: caption,
                    hashtagsPart: hashtags
                }),
                caption: formattedCaption,
                imageUrl: compressedImageUrl
            };

            // Send the post data to the server
            const response = await fetch('/api/posts', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
                },
                body: JSON.stringify(postData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || `API error: ${response.status}`);
            }

            const data = await response.json();

            // Set the post as saved
            setPostSaved(true);

            // If we have a post ID, update the URL
            if (data.postId) {
                setSavedPostId(data.postId);

                // Update the URL to include the post ID without reloading the page
                if (!postId) {
                    navigate(`/create/${data.postId}`, { replace: true });
                }
            }

            toast({
                title: "Post saved",
                description: "Your post has been saved to your account",
            });

        } catch (error) {
            console.error('Error saving post:', error);
            toast({
                title: "Save failed",
                description: error instanceof Error ? error.message : "An unknown error occurred",
                variant: "destructive"
            });
        } finally {
            setIsSaving(false);
        }
    };

    // Image size options with better descriptions - organized as requested
    const imageSizeOptions = [
        // Auto option (recommended)
        { value: "Auto", name: "Auto", ratio: 1 / 1, description: "Recommended", icon: <Sparkles className="w-5 h-5" />, recommended: true },

        // First row
        { value: "1:1", name: "Square", ratio: 1 / 1, description: "Instagram, Facebook", icon: <LayoutGrid className="w-5 h-5" /> },
        { value: "5:4", name: "Card", ratio: 5 / 4, description: "X (Twitter) cards", icon: <LayoutGrid className="w-5 h-4" /> },
        { value: "3:2", name: "Landscape+", ratio: 3 / 2, description: "Facebook", icon: <Maximize className="w-5 h-3.5" /> },
        { value: "16:9", name: "Landscape", ratio: 16 / 9, description: "X (Twitter), LinkedIn", icon: <Maximize className="w-5 h-3" /> },

        // Second row
        { value: "4:5", name: "Portrait", ratio: 4 / 5, description: "Instagram optimal", icon: <ImageIcon className="w-4 h-5" /> },
        { value: "2:3", name: "Pins", ratio: 2 / 3, description: "Pin format", icon: <ImageIcon className="w-3.5 h-5" /> },
        { value: "9:16", name: "Story", ratio: 9 / 16, description: "Stories, Reels", icon: <Minimize className="w-3 h-5" /> },
    ];

    return (
        <div className="flex flex-col min-h-screen">
            <section className="section-padding">
                <div className="max-w-4xl mx-auto">
                    <div className="mb-8">
                        <Link to="/" className="flex items-center text-muted-foreground hover:text-foreground transition-colors">
                            <ArrowLeft className="h-4 w-4 mr-2" /> Back to Home
                        </Link>
                    </div>
                    <div className="text-center mb-12">
                        <h1 className="text-3xl md:text-4xl font-bold mb-4">Advanced Content Generator</h1>
                        <p className="text-lg text-muted-foreground">
                            Customize your AI-generated content with precise options
                        </p>
                    </div>

                    {profile && hasReachedPostsLimit && (
                        <Alert variant="destructive" className="mb-6">
                            <AlertTriangle className="h-4 w-4" />
                            <AlertTitle>Posts limit reached</AlertTitle>
                            <AlertDescription>
                                You've used all {profile.posts_limit} posts for your {profile.plan} plan this month.
                                <div className="mt-2">
                                    <Button variant="outline" size="sm" asChild>
                                        <Link to="/pricing">Upgrade Plan</Link>
                                    </Button>
                                </div>
                            </AlertDescription>
                        </Alert>
                    )}

                    {(loading || (postId && !user && !loading)) && (
                        <div className="text-center p-8 mb-6">
                            <div className="animate-spin mb-2">◌</div>
                            <p>{postId && !user ? "Please log in to view this post..." : "Loading account information..."}</p>
                        </div>
                    )}

                    {isLoadingPost && (
                        <Card className="text-center p-8 mb-6 border-primary/50 bg-primary/5">
                            <div className="flex flex-col items-center justify-center">
                                <div className="animate-spin mb-2 text-2xl text-primary">◌</div>
                                <p className="font-medium">Loading your saved post...</p>
                                <p className="text-sm text-muted-foreground mt-1">Please wait while we retrieve your content</p>
                            </div>
                        </Card>
                    )}

                    <Card>
                        <CardHeader>
                            <CardTitle>Content Specifications</CardTitle>
                            <CardDescription>
                                Provide details about the content you want to create
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={handleSubmit} className="space-y-6">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div className="space-y-2">
                                        <label className="text-sm font-medium">Platform</label>
                                        <select
                                            className="w-full border rounded-md px-3 py-2 bg-background"
                                            value={platform}
                                            onChange={(e) => setPlatform(e.target.value)}
                                        >
                                            <option value="Instagram">Instagram</option>
                                            <option value="X">X (Twitter)</option>
                                            <option value="LinkedIn">LinkedIn</option>
                                            <option value="Facebook">Facebook</option>
                                            <option value="Other">Other (specify)</option>
                                        </select>
                                        {platform === "Other" && (
                                            <input
                                                className="w-full border rounded-md px-3 py-2 mt-2 bg-background"
                                                placeholder="Specify platform"
                                                value={customPlatform}
                                                onChange={(e) => setCustomPlatform(e.target.value)}
                                            />
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <label className="text-sm font-medium">Tone</label>
                                        <select
                                            className="w-full border rounded-md px-3 py-2 bg-background"
                                            value={tone}
                                            onChange={(e) => setTone(e.target.value)}
                                        >
                                            <option value="Professional">Professional</option>
                                            <option value="Casual">Casual</option>
                                            <option value="Enthusiastic">Enthusiastic</option>
                                            <option value="Informative">Informative</option>
                                            <option value="Humorous">Humorous</option>
                                            <option value="Inspirational">Inspirational</option>
                                            <option value="Formal">Formal</option>
                                            <option value="Friendly">Friendly</option>
                                            <option value="Other">Other (specify)</option>
                                        </select>
                                        {tone === "Other" && (
                                            <input
                                                className="w-full border rounded-md px-3 py-2 mt-2 bg-background"
                                                placeholder="Specify tone"
                                                value={customTone}
                                                onChange={(e) => setCustomTone(e.target.value)}
                                            />
                                        )}
                                    </div>

                                    <div className="space-y-2 md:col-span-2">
                                        <label className="text-sm font-medium">Topic</label>
                                        <textarea
                                            className="w-full border rounded-md px-3 py-2 bg-background min-h-[100px]"
                                            placeholder="Describe your topic in detail. For example: 'Grand opening of our new downtown coffee shop with organic beans and vegan pastries.'"
                                            value={topic}
                                            onChange={(e) => setTopic(e.target.value)}
                                        />
                                    </div>

                                    <div className="space-y-4 md:col-span-2">
                                        <div className="flex items-center gap-2">
                                            <ImageIcon className="h-5 w-5 text-primary" />
                                            <label className="text-sm font-medium">Image Size</label>
                                        </div>
                                        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                                            {imageSizeOptions.map((option) => (
                                                <div key={option.value} className="relative">
                                                    <button
                                                        type="button"
                                                        onClick={() => setImageSize(option.value)}
                                                        className={`w-full cursor-pointer rounded-lg border-2 overflow-hidden transition-all focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 ${imageSize === option.value
                                                                ? "border-primary shadow-md"
                                                                : "border-border hover:border-primary/50"
                                                            }`}
                                                    >
                                                        <AspectRatio ratio={option.ratio} className="bg-muted">
                                                            <div className="w-full h-full flex flex-col items-center justify-center p-2 bg-gradient-to-br from-muted/50 to-muted/90">
                                                                <div className="mb-1 text-primary">
                                                                    {option.icon}
                                                                </div>
                                                                <div className="text-center">
                                                                    <div className="text-xs font-semibold">{option.value}</div>
                                                                </div>
                                                            </div>
                                                        </AspectRatio>
                                                    </button>
                                                    <div className="text-xs text-center mt-1 text-muted-foreground">
                                                        {option.description}
                                                    </div>
                                                    {option.recommended && (
                                                        <div className="absolute -top-2 -right-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs px-2 py-0.5 rounded-full shadow-sm font-medium">
                                                            Recommended
                                                        </div>
                                                    )}
                                                    {imageSize === option.value && (
                                                        <div className="absolute -top-1 -right-1 bg-primary text-white rounded-full p-0.5 shadow-sm">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round">
                                                                <polyline points="20 6 9 17 4 12"></polyline>
                                                            </svg>
                                                        </div>
                                                    )}
                                                </div>
                                            ))}
                                        </div>
                                        <div className="bg-muted/30 p-4 rounded-lg border mt-2">
                                            <div className="text-xs text-muted-foreground">
                                                <span className="font-medium block mb-1">Usage tips:</span>
                                                <ul className="list-disc pl-4 space-y-1">
                                                    <li>Square (1:1) works best for Instagram feed and Facebook posts</li>
                                                    <li>Portrait (4:5) maximizes screen space on Instagram</li>
                                                    <li>Story (9:16) is ideal for full-screen vertical content</li>
                                                    <li>Pinterest (2:3) performs best on Pinterest boards</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="space-y-4 md:col-span-2">
                                        <div className="flex items-center gap-2">
                                            <Settings className="h-5 w-5 text-primary" />
                                            <label className="text-sm font-medium">Generation Quality</label>
                                        </div>
                                        <div className="grid grid-cols-3 gap-3">
                                            {[
                                                { value: "FAST", name: "Fast", icon: <Zap className="h-5 w-5" />, description: "Quick generation with good results" },
                                                { value: "BALANCED", name: "Balanced", icon: <BarChart2 className="h-5 w-5" />, description: "Optimal balance of speed and quality" },
                                                {
                                                    value: "QUALITY",
                                                    name: "Quality",
                                                    icon: <Sparkles className="h-5 w-5" />,
                                                    description: "Highest quality output (slower)",
                                                    disabled: profile?.plan === 'free'
                                                }
                                            ].map((option) => (
                                                <div key={option.value} className="relative">
                                                    <button
                                                        type="button"
                                                        onClick={() => {
                                                            if (option.disabled) {
                                                                toast({
                                                                    title: "Feature not available",
                                                                    description: "Quality mode is only available on paid plans. Please upgrade to use this feature.",
                                                                    variant: "destructive"
                                                                });
                                                                navigate("/pricing");
                                                            } else {
                                                                setGenerationQuality(option.value);
                                                            }
                                                        }}
                                                        className={`w-full flex flex-col items-center justify-center p-4 rounded-lg border-2 transition-all ${generationQuality === option.value
                                                                ? "border-primary bg-primary/5 shadow-sm"
                                                                : option.disabled
                                                                    ? "border-border bg-muted/50 opacity-60 cursor-not-allowed"
                                                                    : "border-border hover:border-primary/50 bg-card"
                                                            }`}
                                                    >
                                                        <div className={`mb-2 ${generationQuality === option.value
                                                                ? "text-primary"
                                                                : option.disabled
                                                                    ? "text-muted-foreground/70"
                                                                    : "text-muted-foreground"
                                                            }`}>
                                                            {option.icon}
                                                        </div>
                                                        <div className={`text-sm font-medium ${option.disabled ? "text-muted-foreground/70" : ""}`}>
                                                            {option.name}
                                                            {option.disabled && (
                                                                <span className="ml-1 text-xs bg-muted-foreground/20 px-1 py-0.5 rounded text-muted-foreground">
                                                                    PAID
                                                                </span>
                                                            )}
                                                        </div>
                                                        <div className={`text-xs text-center mt-1 ${option.disabled ? "text-muted-foreground/60" : "text-muted-foreground"
                                                            }`}>
                                                            {option.description}
                                                        </div>
                                                    </button>
                                                    {generationQuality === option.value && !option.disabled && (
                                                        <div className="absolute -top-1 -right-1 bg-primary text-white rounded-full p-0.5 shadow-sm">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round">
                                                                <polyline points="20 6 9 17 4 12"></polyline>
                                                            </svg>
                                                        </div>
                                                    )}
                                                </div>
                                            ))}
                                        </div>
                                        <div className="text-xs text-muted-foreground mt-1">
                                            <span className="inline-flex items-center">
                                                <span className="bg-primary/10 text-primary rounded-full p-1 mr-2">
                                                    <Settings className="h-3 w-3" />
                                                </span>
                                                Higher quality settings produce better results but take longer to generate.
                                            </span>
                                        </div>
                                    </div>

                                    <div className="space-y-4 md:col-span-2">
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center gap-2">
                                                <Hash className="h-5 w-5 text-primary" />
                                                <label className="text-sm font-medium">Number of Hashtags</label>
                                            </div>
                                            <span className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-primary font-medium text-sm">{hashtagCount}</span>
                                        </div>
                                        <div className="bg-muted/30 p-4 rounded-lg border">
                                            <div className="space-y-3">
                                                <input
                                                    type="range"
                                                    min="0"
                                                    max="10"
                                                    value={hashtagCount}
                                                    onChange={(e) => setHashtagCount(parseInt(e.target.value))}
                                                    className="w-full accent-primary"
                                                />
                                                <div className="flex justify-between px-1">
                                                    {[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((value) => (
                                                        <button
                                                            key={value}
                                                            type="button"
                                                            onClick={() => setHashtagCount(value)}
                                                            className={`w-6 h-6 rounded-full flex items-center justify-center text-xs transition-all ${hashtagCount === value
                                                                    ? "bg-primary text-primary-foreground font-medium"
                                                                    : "text-muted-foreground hover:bg-muted/80"
                                                                }`}
                                                        >
                                                            {value}
                                                        </button>
                                                    ))}
                                                </div>
                                                <div className="flex justify-between text-xs text-muted-foreground mt-1">
                                                    <span className="font-medium">None</span>
                                                    <span className="font-medium">Few</span>
                                                    <span className="font-medium">Many</span>
                                                </div>
                                                <div className="mt-2 bg-background/50 rounded border p-2 text-sm">
                                                    <p className="flex gap-1 items-center text-muted-foreground">
                                                        <span>Preview:</span>
                                                        <span className="text-foreground font-medium">
                                                            {hashtagCount === 0 ? (
                                                                <span className="text-muted-foreground italic">No hashtags will be added</span>
                                                            ) : (
                                                                Array(hashtagCount)
                                                                    .fill(0)
                                                                    .map((_, i) => (
                                                                        <span key={i} className="mr-1 text-primary">#{`tag${i + 1}`}</span>
                                                                    ))
                                                            )}
                                                        </span>
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div className="pt-4">
                                    <Button
                                        type="submit"
                                        disabled={isLoading || !!postId}
                                        className="w-full gradient-button"
                                        size="lg"
                                    >
                                        {isLoading ? (
                                            <span className="flex items-center">
                                                <span className="animate-spin mr-2">◌</span> Generating...
                                            </span>
                                        ) : postId ? (
                                            <span className="flex items-center">
                                                <Wand2 className="mr-2 h-5 w-5" /> Viewing Existing Post
                                            </span>
                                        ) : (
                                            <span className="flex items-center">
                                                <Wand2 className="mr-2 h-5 w-5" /> Generate Custom Content
                                            </span>
                                        )}
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>

                    {((generatedContent || (postId && !isLoadingPost)) && user) && (
                        <Card className="mt-8">
                            <CardHeader>
                                <CardTitle>Generated Content</CardTitle>
                                <CardDescription>
                                    Your AI-generated social media post for {platform === "Other" ? customPlatform : platform}
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div className="space-y-2">
                                    <div className="flex items-center justify-between">
                                        <h3 className="text-lg font-medium">Caption</h3>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            className="h-8 gap-1"
                                            onClick={() => handleCopy('caption')}
                                            disabled={!generatedContent}
                                        >
                                            {copyStatus.caption ? (
                                                <><Check className="h-4 w-4" /> Copied</>
                                            ) : (
                                                <><Copy className="h-4 w-4" /> Copy</>
                                            )}
                                        </Button>
                                    </div>
                                    <div className="p-4 bg-muted/30 rounded-lg border whitespace-pre-wrap">
                                        {generatedContent ? generatedContent.CAPTION : (
                                            <div className="text-muted-foreground italic">
                                                {postId ? "Loading caption..." : "No caption available"}
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {(generatedContent?.HASHTAGS || postId) && (
                                    <div className="space-y-2">
                                        <div className="flex items-center justify-between">
                                            <h3 className="text-lg font-medium">Hashtags</h3>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                className="h-8 gap-1"
                                                onClick={() => handleCopy('hashtags')}
                                                disabled={!generatedContent?.HASHTAGS}
                                            >
                                                {copyStatus.hashtags ? (
                                                    <><Check className="h-4 w-4" /> Copied</>
                                                ) : (
                                                    <><Copy className="h-4 w-4" /> Copy</>
                                                )}
                                            </Button>
                                        </div>
                                        <div className="p-4 bg-muted/30 rounded-lg border">
                                            {generatedContent?.HASHTAGS ? (
                                                <div className="flex flex-wrap gap-2">
                                                    {generatedContent.HASHTAGS.split(' ').map((tag, index) => (
                                                        <span key={index} className="bg-primary/10 text-primary px-2 py-1 rounded-full text-sm">
                                                            {tag}
                                                        </span>
                                                    ))}
                                                </div>
                                            ) : (
                                                <div className="text-muted-foreground italic">
                                                    {postId ? "Loading hashtags..." : "No hashtags available"}
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                )}

                                <div className="space-y-2">
                                    <div className="flex items-center justify-between">
                                        <h3 className="text-lg font-medium">Generated Image</h3>
                                        <div className="flex gap-2">
                                            {generatedImage && (
                                                <>
                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                        className="h-8 gap-1"
                                                        onClick={handleDownloadImage}
                                                    >
                                                        <Download className="h-4 w-4" /> Download
                                                    </Button>
                                                </>
                                            )}
                                            {!generatedImage && !isGeneratingImage && generatedContent && (
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    className="h-8 gap-1"
                                                    onClick={() => {
                                                        if (postId) {
                                                            // If we have a post ID but no image, try to reload the post
                                                            const authToken = localStorage.getItem("auth_token");
                                                            if (authToken) {
                                                                console.log('Manually triggering image reload');
                                                                fetch(`/api/posts/${postId}`, {
                                                                    headers: { Authorization: `Bearer ${authToken}` }
                                                                })
                                                                    .then(response => response.json())
                                                                    .then(data => {
                                                                        if (data.imageData) {
                                                                            setGeneratedImage(data.imageData);
                                                                        } else if (generatedContent?.IMAGE_PROMPT) {
                                                                            // If no image data, try to generate a new one if we have a prompt
                                                                            generateImage(generatedContent.IMAGE_PROMPT);
                                                                        } else {
                                                                            console.warn('No image data and no image prompt available');
                                                                            toast({
                                                                                title: "Cannot generate image",
                                                                                description: "No image prompt available",
                                                                                variant: "destructive"
                                                                            });
                                                                        }
                                                                    })
                                                                    .catch(err => {
                                                                        console.error('Error reloading image:', err);
                                                                        // Fallback to generating a new image if we have a prompt
                                                                        if (generatedContent?.IMAGE_PROMPT) {
                                                                            generateImage(generatedContent.IMAGE_PROMPT);
                                                                        }
                                                                    });
                                                            }
                                                        } else if (generatedContent?.IMAGE_PROMPT) {
                                                            // If no post ID, just generate a new image if we have a prompt
                                                            generateImage(generatedContent.IMAGE_PROMPT);
                                                        } else {
                                                            console.warn('No image prompt available');
                                                            toast({
                                                                title: "Cannot generate image",
                                                                description: "No image prompt available",
                                                                variant: "destructive"
                                                            });
                                                        }
                                                    }}
                                                >
                                                    <Wand2 className="h-4 w-4 mr-1" /> {postId ? 'Reload Image' : 'Generate Image'}
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                    <div className="overflow-hidden rounded-lg border">
                                        <AspectRatio
                                            ratio={
                                                (() => {
                                                    // If we have an actual image and "Auto" is selected, use actual ratio
                                                    if (imageSize === "Auto" && generatedImage && actualImageAspectRatio !== 1) {
                                                        return actualImageAspectRatio;
                                                    }
                                                    // If "Auto" but no image yet, default to square
                                                    if (imageSize === "Auto") {
                                                        return 1;
                                                    }
                                                    try {
                                                        const [width, height] = imageSize.split(':').map(Number);
                                                        return width / height;
                                                    } catch {
                                                        return 1; // Default fallback
                                                    }
                                                })()
                                            }
                                        >
                                            {isGeneratingImage || (postId && !generatedImage && isLoadingPost) ? (
                                                <div className="w-full h-full flex items-center justify-center bg-muted/30">
                                                    <div className="flex flex-col items-center">
                                                        <div className="animate-spin mb-2">◌</div>
                                                        <p className="text-sm text-muted-foreground">
                                                            {isGeneratingImage ? 'Generating image...' : 'Loading image...'}
                                                        </p>
                                                    </div>
                                                </div>
                                            ) : generatedImage ? (
                                                <img
                                                    src={generatedImage}
                                                    alt="Generated image"
                                                    className="w-full h-full object-cover"
                                                    onError={(e) => {
                                                        console.error('Image failed to load');
                                                        // If image fails to load, show error state
                                                        (e.target as HTMLImageElement).style.display = 'none';
                                                        (e.target as HTMLImageElement).parentElement!.innerHTML = `
                              <div class="w-full h-full flex items-center justify-center bg-muted/30">
                                <div class="text-center p-4">
                                  <p class="text-sm text-destructive">Error loading image</p>
                                  <p class="text-xs text-muted-foreground mt-1">Try reloading the page</p>
                                </div>
                              </div>
                            `;
                                                    }}
                                                />
                                            ) : (
                                                <div className="w-full h-full flex items-center justify-center bg-muted/30">
                                                    <div className="text-center p-4">
                                                        {imageLoadAttempted ? (
                                                            <>
                                                                <AlertTriangle className="h-8 w-8 mx-auto mb-2 text-destructive" />
                                                                <p className="text-sm text-destructive">
                                                                    Image failed to load
                                                                </p>
                                                                <p className="text-xs text-muted-foreground mt-1">
                                                                    The image may be missing or corrupted
                                                                </p>
                                                                <div className="flex justify-center gap-2 mt-3">
                                                                    <Button
                                                                        variant="outline"
                                                                        size="sm"
                                                                        onClick={() => window.location.reload()}
                                                                    >
                                                                        Reload page
                                                                    </Button>
                                                                    {generatedContent?.IMAGE_PROMPT && (
                                                                        <Button
                                                                            variant="outline"
                                                                            size="sm"
                                                                            onClick={() => generateImage(generatedContent.IMAGE_PROMPT)}
                                                                        >
                                                                            Regenerate image
                                                                        </Button>
                                                                    )}
                                                                </div>
                                                            </>
                                                        ) : (
                                                            <>
                                                                <ImageIcon className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                                                                <p className="text-sm text-muted-foreground">
                                                                    {postId ? 'No image available' : 'Image will be generated automatically'}
                                                                </p>
                                                                {postId && (
                                                                    <Button
                                                                        variant="ghost"
                                                                        size="sm"
                                                                        className="mt-2"
                                                                        onClick={() => window.location.reload()}
                                                                    >
                                                                        Reload page
                                                                    </Button>
                                                                )}
                                                            </>
                                                        )}
                                                    </div>
                                                </div>
                                            )}
                                        </AspectRatio>
                                    </div>
                                    {generatedImage && (
                                        <div className="flex justify-end mt-2">
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                className="h-7 gap-1 text-muted-foreground hover:text-foreground"
                                                onClick={handlePreviewImage}
                                            >
                                                <ExternalLink className="h-3.5 w-3.5" /> Open in new tab
                                            </Button>
                                        </div>
                                    )}

                                    {/* Hidden image prompt section that can be toggled */}
                                    {generatedContent && (
                                        <details className="mt-4">
                                            <summary className="cursor-pointer text-sm text-muted-foreground hover:text-foreground">
                                                View Image Prompt
                                            </summary>
                                            <div className="mt-2 p-4 bg-muted/30 rounded-lg border whitespace-pre-wrap text-sm">
                                                {generatedContent.IMAGE_PROMPT || 'No image prompt available'}
                                                <div className="mt-2 flex justify-end">
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        className="h-7 gap-1"
                                                        onClick={() => handleCopy('imagePrompt')}
                                                    >
                                                        {copyStatus.imagePrompt ? (
                                                            <><Check className="h-3 w-3" /> Copied</>
                                                        ) : (
                                                            <><Copy className="h-3 w-3" /> Copy</>
                                                        )}
                                                    </Button>
                                                </div>
                                            </div>
                                        </details>
                                    )}
                                </div>

                                <div className="flex justify-center gap-3 pt-4">
                                    {generatedImage && (
                                        <Button
                                            variant="outline"
                                            size="lg"
                                            className="gap-2"
                                            onClick={handlePreviewImage}
                                        >
                                            <ExternalLink className="h-5 w-5" /> Preview
                                        </Button>
                                    )}
                                    {postSaved && (
                                        <Button
                                            variant="outline"
                                            size="lg"
                                            className="gap-2"
                                            disabled={true}
                                        >
                                            <Database className="h-5 w-5" /> Saved to Database
                                        </Button>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    )}
                </div>
            </section>
        </div>
    );
}
