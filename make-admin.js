#!/usr/bin/env node

import { db } from './server/sqlite-db.js';

const email = process.argv[2];

if (!email) {
  console.log('Usage: node make-admin.js <user-email>');
  console.log('Example: node make-admin.js <EMAIL>');
  process.exit(1);
}

try {
  // Find user by email
  const user = db.prepare(`
    SELECT u.id, u.email, p.full_name, p.is_admin 
    FROM users u 
    JOIN profiles p ON u.id = p.id 
    WHERE u.email = ?
  `).get(email);

  if (!user) {
    console.log(`User with email ${email} not found`);
    process.exit(1);
  }

  // Update user to admin
  const result = db.prepare('UPDATE profiles SET is_admin = 1 WHERE id = ?').run(user.id);
  
  if (result.changes > 0) {
    console.log(`✅ Successfully granted admin privileges to ${user.full_name} (${email})`);
    console.log(`User ID: ${user.id}`);
  } else {
    console.log('❌ Failed to update user');
  }
} catch (error) {
  console.error('Error making user admin:', error);
  process.exit(1);
}