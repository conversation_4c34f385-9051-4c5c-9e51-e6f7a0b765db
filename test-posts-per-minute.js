import fetch from 'node-fetch';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';
import Database from 'better-sqlite3';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Database setup
const dbPath = join(__dirname, 'data', 'socialspark.db');
const db = new Database(dbPath);

// Test configuration
const SERVER_URL = 'http://localhost:5001';
const TEST_USER_EMAIL = '<EMAIL>';
const TEST_USER_PASSWORD = 'testpassword123';

// Helper function to make authenticated requests
async function makeAuthenticatedRequest(url, options = {}, token) {
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers
  };
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  return fetch(url, {
    ...options,
    headers
  });
}

// Function to get or create test user
async function getTestUser() {
  try {
    // Try to login first
    const loginResponse = await fetch(`${SERVER_URL}/api/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: TEST_USER_EMAIL,
        password: TEST_USER_PASSWORD
      })
    });

    if (loginResponse.ok) {
      const loginData = await loginResponse.json();
      console.log('✅ Logged in with existing test user');
      return loginData.token;
    }

    // If login fails, try to register
    console.log('🔄 Test user not found, creating new one...');
    const registerResponse = await fetch(`${SERVER_URL}/api/auth/register`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: TEST_USER_EMAIL,
        password: TEST_USER_PASSWORD,
        fullName: 'Test User'
      })
    });

    if (registerResponse.ok) {
      const registerData = await registerResponse.json();
      console.log('✅ Created new test user');
      return registerData.token;
    }

    throw new Error('Failed to get or create test user');
  } catch (error) {
    console.error('❌ Error getting test user:', error);
    throw error;
  }
}

// Function to create a test post
async function createTestPost(token, index) {
  const testContent = {
    platform: 'instagram',
    tone: 'professional',
    topic: `Test post ${index}`,
    captionPart: `This is test post number ${index}`,
    hashtagsPart: '#test #post #number' + index
  };

  const response = await makeAuthenticatedRequest(`${SERVER_URL}/api/posts`, {
    method: 'POST',
    body: JSON.stringify({
      content: JSON.stringify(testContent),
      caption: `${testContent.captionPart}\n\n${testContent.hashtagsPart}`
    })
  }, token);

  if (response.ok) {
    const data = await response.json();
    console.log(`✅ Created test post ${index}: ${data.postId}`);
    return data.postId;
  } else {
    const error = await response.text();
    console.error(`❌ Failed to create test post ${index}:`, error);
    return null;
  }
}

// Function to get admin stats
async function getAdminStats() {
  try {
    // For this test, we'll query the database directly since we don't have admin credentials
    const result = db.prepare(`
      SELECT COUNT(*) as count
      FROM posts
      WHERE created_at >= ?
    `).get(new Date(Date.now() - 60 * 1000).toISOString());

    return result.count;
  } catch (error) {
    console.error('❌ Error getting admin stats:', error);
    return 0;
  }
}

// Function to get posts from different time periods for comparison
async function getPostsInTimeRanges() {
  const now = new Date();
  const oneMinuteAgo = new Date(now.getTime() - 60 * 1000).toISOString();
  const twoMinutesAgo = new Date(now.getTime() - 120 * 1000).toISOString();
  const threeMinutesAgo = new Date(now.getTime() - 180 * 1000).toISOString();

  const postsLastMinute = db.prepare(`
    SELECT COUNT(*) as count FROM posts WHERE created_at >= ?
  `).get(oneMinuteAgo).count;

  const postsLast2Minutes = db.prepare(`
    SELECT COUNT(*) as count FROM posts WHERE created_at >= ?
  `).get(twoMinutesAgo).count;

  const postsLast3Minutes = db.prepare(`
    SELECT COUNT(*) as count FROM posts WHERE created_at >= ?
  `).get(threeMinutesAgo).count;

  return {
    lastMinute: postsLastMinute,
    last2Minutes: postsLast2Minutes,
    last3Minutes: postsLast3Minutes
  };
}

// Main test function
async function testPostsPerMinute() {
  console.log('🚀 Starting Posts/Minute test...\n');

  try {
    // Get test user token
    const token = await getTestUser();

    // Get initial stats
    console.log('📊 Initial stats:');
    const initialStats = await getPostsInTimeRanges();
    console.log(`   Posts in last minute: ${initialStats.lastMinute}`);
    console.log(`   Posts in last 2 minutes: ${initialStats.last2Minutes}`);
    console.log(`   Posts in last 3 minutes: ${initialStats.last3Minutes}\n`);

    // Create 2 test posts
    console.log('📝 Creating test posts...');
    await createTestPost(token, 1);
    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
    await createTestPost(token, 2);

    // Get stats immediately after creating posts
    console.log('\n📊 Stats immediately after creating 2 posts:');
    const immediateStats = await getPostsInTimeRanges();
    console.log(`   Posts in last minute: ${immediateStats.lastMinute}`);
    console.log(`   Posts in last 2 minutes: ${immediateStats.last2Minutes}`);
    console.log(`   Posts in last 3 minutes: ${immediateStats.last3Minutes}\n`);

    // Wait 65 seconds and check again
    console.log('⏳ Waiting 65 seconds to test time-based filtering...');
    await new Promise(resolve => setTimeout(resolve, 65000));

    console.log('\n📊 Stats after waiting 65 seconds:');
    const finalStats = await getPostsInTimeRanges();
    console.log(`   Posts in last minute: ${finalStats.lastMinute}`);
    console.log(`   Posts in last 2 minutes: ${finalStats.last2Minutes}`);
    console.log(`   Posts in last 3 minutes: ${finalStats.last3Minutes}\n`);

    // Verify the fix
    if (finalStats.lastMinute === 0 && finalStats.last2Minutes === 2) {
      console.log('✅ SUCCESS: Posts/Minute calculation is working correctly!');
      console.log('   - Posts older than 1 minute are correctly excluded');
      console.log('   - Posts within 2 minutes are still counted in the 2-minute range');
    } else {
      console.log('❌ ISSUE: Posts/Minute calculation may still have problems');
      console.log(`   Expected: 0 posts in last minute, 2 posts in last 2 minutes`);
      console.log(`   Actual: ${finalStats.lastMinute} posts in last minute, ${finalStats.last2Minutes} posts in last 2 minutes`);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    db.close();
  }
}

// Run the test
testPostsPerMinute();
