
import { LayoutGrid, Sliders, Image, Download, Instagram, Linkedin, Facebook, Check, Zap } from "lucide-react"
import XIcon from "@/components/icons/XIcon"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Link } from "react-router-dom"

export default function Features() {
  const features = [
    {
      title: "Smart Templates",
      description: "Optimized prompts for X (Twitter), Instagram, LinkedIn & Facebook that follow platform best practices and character limits.",
      icon: LayoutGrid,
      color: "bg-blue-500/10 text-blue-500",
      list: [
        "Platform-specific formatting",
        "Character count optimization",
        "Hashtag recommendations",
        "Emoji suggestions"
      ]
    },
    {
      title: "Custom Tone & Topics",
      description: "Choose your brand voice and themes with ease. From professional to casual, serious to playful - match your unique style.",
      icon: Sliders,
      color: "bg-purple-500/10 text-purple-500",
      list: [
        "Voice customization",
        "Tone presets",
        "Industry-specific content",
        "Brand vocabulary integration"
      ]
    },
    {
      title: "Instant Preview Gallery",
      description: "See matching images and captions side-by-side. No more hunting for the perfect visual to match your text.",
      icon: Image,
      color: "bg-green-500/10 text-green-500",
      list: [
        "AI-matched visuals",
        "Caption-image pairing",
        "Multiple preview options",
        "Style consistency"
      ]
    },
    {
      title: "Quick Export",
      description: "Download or copy ready-to-post content effortlessly. Push directly to scheduling tools or save for later use.",
      icon: Download,
      color: "bg-amber-500/10 text-amber-500",
      list: [
        "One-click copying",
        "Direct downloads",
        "Scheduling tool integration",
        "Content library storage"
      ]
    },
  ]

  const platforms = [
    { name: "X (Twitter)", icon: XIcon, color: "text-gray-800" },
    { name: "Instagram", icon: Instagram, color: "text-pink-500" },
    { name: "LinkedIn", icon: Linkedin, color: "text-blue-700" },
    { name: "Facebook", icon: Facebook, color: "text-blue-600" },
  ]

  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="section-padding bg-gradient-to-br from-background to-accent/10">
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">Why SocialSparkGen?</h1>
          <p className="text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto mb-12">
            Our AI-powered platform takes the guesswork out of social media content creation,
            giving you more time to focus on what matters most - growing your audience.
          </p>

          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {platforms.map((platform) => (
              <div key={platform.name} className="flex items-center gap-2 bg-card px-4 py-2 rounded-full shadow-sm">
                <platform.icon className={`h-5 w-5 ${platform.color}`} />
                <span>{platform.name}</span>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="section-padding">
        <div className="max-w-7xl mx-auto">
          <div className="space-y-24">
            {features.map((feature, index) => (
              <div key={index} className="grid md:grid-cols-2 gap-12 items-center">
                <div className={index % 2 === 1 ? "md:order-2" : ""}>
                  <div className={`inline-flex ${feature.color} p-3 rounded-lg mb-4`}>
                    <feature.icon className="h-6 w-6" />
                  </div>
                  <h2 className="text-3xl font-bold mb-4">{feature.title}</h2>
                  <p className="text-lg text-muted-foreground mb-6">{feature.description}</p>
                  <ul className="space-y-3">
                    {feature.list.map((item, i) => (
                      <li key={i} className="flex items-start gap-3">
                        <Check className="h-5 w-5 text-primary mt-0.5 shrink-0" />
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                <div className={`bg-card border rounded-xl shadow-lg p-6 ${index % 2 === 1 ? "md:order-1" : ""}`}>
                  <div className="bg-primary/5 rounded-lg aspect-video flex items-center justify-center">
                    <feature.icon className="h-16 w-16 text-primary opacity-50" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="section-padding bg-gradient-to-br from-background to-accent/10">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Proven Results for Creators Like You
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              SocialSparkGen is helping thousands of content creators achieve better results
              with less effort. Here's what our platform delivers:
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {[
              { number: "70%", text: "Time Saved on Content Creation" },
              { number: "3.4x", text: "Average Engagement Increase" },
              { number: "80%", text: "Users Post More Consistently" },
              { number: "92%", text: "User Satisfaction Rate" },
            ].map((stat, i) => (
              <div key={i} className="text-center">
                <p className="text-4xl md:text-5xl font-bold text-primary mb-2">{stat.number}</p>
                <p className="text-muted-foreground">{stat.text}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section-padding">
        <div className="max-w-3xl mx-auto text-center">
          <div className="inline-flex bg-primary/10 p-3 rounded-full mb-4">
            <Zap className="h-6 w-6 text-primary" />
          </div>
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Spark Your Social Media Presence?
          </h2>
          <p className="text-lg text-muted-foreground mb-8">
            Join thousands of creators saving time while creating better content.
            No credit card required to get started.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="gradient-button text-lg px-8">
              Try It Free
            </Button>
            <Button
              size="lg"
              variant="default"
              className="font-medium border border-primary/30 bg-primary/10 text-primary hover:bg-primary/20"
              asChild
            >
              <Link to="/pricing">View Pricing</Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}
