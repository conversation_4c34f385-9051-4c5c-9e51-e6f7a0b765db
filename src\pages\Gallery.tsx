import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";

// Helper component to dynamically calculate aspect ratio from loaded image
const DynamicAspectRatio = ({ imageSize, imageData, children, className }: {
  imageSize: string;
  imageData: string;
  children: React.ReactNode;
  className?: string;
}) => {
  const [actualRatio, setActualRatio] = useState<number>(() => {
    if (imageSize === "Auto") return 1;
    try {
      const [width, height] = imageSize.split(":").map(Number);
      return width / height;
    } catch {
      return 1;
    }
  });

  useEffect(() => {
    if (imageData) {
      const img = new Image();
      img.onload = () => {
        const ratio = img.width / img.height;
        setActualRatio(ratio);
      };
      img.src = imageData;
    }
  }, [imageData]);

  return (
    <AspectRatio ratio={actualRatio} className={className}>
      {children}
    </AspectRatio>
  );
};
import { useAuth } from "@/context/AuthContext";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,

} from "@/components/ui/pagination";
import {
  Trash2,
  Copy,
  Download,
  MoreVertical,
  ExternalLink,
  Hash,
  Loader2,
  ImageIcon,
  AlertTriangle,
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";

// Define types for our post objects
interface Post {
  id: string;
  content: {
    platform: string;
    tone: string;
    topic: string;
    hashtagCount: number;
    imageSize: string;
    quality: string;
  };
  caption: string;
  imageUrl: string;
  imageData: string;
  createdAt: string;
}

export default function Gallery() {
  const navigate = useNavigate();
  const { user, loading } = useAuth();
  const { toast } = useToast();
  const [posts, setPosts] = useState<Post[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const [copyStatus, setCopyStatus] = useState<{
    [key: string]: { caption: boolean; hashtags: boolean };
  }>({});

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 6,
    total: 0,
    pages: 0
  });

  // Check if user is logged in
  useEffect(() => {
    if (!loading && !user) {
      navigate("/login", { state: { redirectTo: "/gallery" } });
    }
  }, [user, loading, navigate]);

  // Fetch posts when component mounts or page changes
  useEffect(() => {
    const fetchPosts = async (page = currentPage) => {
      if (!user) return;

      setIsLoading(true);
      try {
        const response = await fetch(`/api/posts?page=${page}&limit=6`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
          },
        });

        if (!response.ok) {
          throw new Error(`API error: ${response.status}`);
        }

        const data = await response.json();
        setPosts(data.posts || []);
        setPagination(data.pagination || { page: 1, limit: 6, total: 0, pages: 0 });

        // Check for posts with missing images but have imageUrl
        const postsWithMissingImages = (data.posts || []).filter((post: any) => !post.imageData && post.imageUrl);
        if (postsWithMissingImages.length > 0) {
          console.log(`Found ${postsWithMissingImages.length} posts with missing images, will attempt to load them`);
          loadMissingImages(postsWithMissingImages);
        }
      } catch (error) {
        console.error("Error fetching posts:", error);
        toast({
          title: "Error loading posts",
          description: error instanceof Error ? error.message : "An unknown error occurred",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    const loadMissingImages = async (postsToLoad: Post[]) => {
      const authToken = localStorage.getItem("auth_token");
      if (!authToken) return;

      const headers = { Authorization: `Bearer ${authToken}` };

      // Process each post one by one to fetch its image
      for (const post of postsToLoad) {
        try {
          console.log(`Attempting to load image for post ${post.id}`);
          const response = await fetch(`/api/posts/${post.id}`, { headers });

          if (!response.ok) continue;

          const postData = await response.json();

          if (postData.imageData) {
            console.log(`Successfully loaded image for post ${post.id}`);
            // Update just this post in the state
            setPosts(currentPosts =>
              currentPosts.map(p =>
                p.id === post.id ? { ...p, imageData: postData.imageData } : p
              )
            );
          }
        } catch (err) {
          console.error(`Failed to load image for post ${post.id}:`, err);
        }
      }
    };

    fetchPosts();
  }, [user, toast, currentPage]);

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Handle post deletion
  const handleDeletePost = async (postId: string) => {
    setIsDeleting(postId);

    try {
      const response = await fetch(`/api/posts/${postId}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
        },
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      // Check if this was the last post on the current page
      if (posts.length === 1 && currentPage > 1) {
        setCurrentPage(currentPage - 1);
      } else {
        // Refetch the current page to get updated data
        const response = await fetch(`/api/posts?page=${currentPage}&limit=6`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
          },
        });

        if (response.ok) {
          const data = await response.json();
          setPosts(data.posts || []);
          setPagination(data.pagination || { page: 1, limit: 6, total: 0, pages: 0 });
        }
      }

      toast({
        title: "Post deleted",
        description: "Your post has been deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting post:", error);
      toast({
        title: "Error deleting post",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(null);
    }
  };

  // Handle copy caption
  const handleCopyCaption = async (postId: string, caption: string) => {
    try {
      await navigator.clipboard.writeText(caption.split("\n\n")[0] || caption);

      // Update copy status
      setCopyStatus((prev) => ({
        ...prev,
        [postId]: {
          ...prev[postId],
          caption: true,
        },
      }));

      // Reset copy status after 2 seconds
      setTimeout(() => {
        setCopyStatus((prev) => ({
          ...prev,
          [postId]: {
            ...prev[postId],
            caption: false,
          },
        }));
      }, 2000);

      toast({
        title: "Copied to clipboard",
        description: "Caption copied to clipboard",
      });
    } catch (error) {
      toast({
        title: "Copy failed",
        description: "Failed to copy to clipboard",
        variant: "destructive",
      });
    }
  };

  // Handle copy hashtags
  const handleCopyHashtags = async (postId: string, caption: string) => {
    try {
      if (!caption) {
        toast({
          title: "No hashtags available",
          description: "There are no hashtags to copy",
          variant: "destructive",
        });
        return;
      }

      const parts = caption.split("\n\n");
      const hashtags = parts.length > 1 ? parts[1] : "";

      if (!hashtags.trim()) {
        toast({
          title: "No hashtags available",
          description: "There are no hashtags to copy",
          variant: "destructive",
        });
        return;
      }

      await navigator.clipboard.writeText(hashtags);

      // Update copy status
      setCopyStatus((prev) => ({
        ...prev,
        [postId]: {
          ...prev[postId],
          hashtags: true,
        },
      }));

      // Reset copy status after 2 seconds
      setTimeout(() => {
        setCopyStatus((prev) => ({
          ...prev,
          [postId]: {
            ...prev[postId],
            hashtags: false,
          },
        }));
      }, 2000);

      toast({
        title: "Copied to clipboard",
        description: "Hashtags copied to clipboard",
      });
    } catch (error) {
      toast({
        title: "Copy failed",
        description: "Failed to copy to clipboard",
        variant: "destructive",
      });
    }
  };

  // Handle download image
  const handleDownloadImage = async (postId: string) => {
    try {
      // Fetch the image data
      const response = await fetch(`/api/posts/${postId}`);

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      const data = await response.json();

      if (!data.imageData) {
        throw new Error("Image data not available");
      }

      // Create a temporary anchor element
      const link = document.createElement("a");
      link.href = data.imageData;
      link.download = `social-spark-image-${postId}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: "Image downloaded",
        description: "Your image has been downloaded",
      });
    } catch (error) {
      console.error("Error downloading image:", error);
      toast({
        title: "Download failed",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
    }
  };

  // Handle view post
  const handleViewPost = (postId: string) => {
    // Pass a flag to indicate we're coming from the gallery and want to load the image
    navigate(`/create/${postId}`, { state: { from: 'gallery', loadImage: true } });
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch (error) {
      return "Unknown date";
    }
  };

  return (
    <div className="container max-w-7xl py-8 px-4">
      <div className="flex flex-col space-y-6">
        <div className="flex justify-between items-center mb-4">
          <div className="flex flex-col space-y-2">
            <h1 className="text-3xl font-bold">Your Gallery</h1>
            <p className="text-muted-foreground">
              View and manage all your generated social media posts
            </p>
          </div>
          <Button onClick={() => navigate("/create")} className="gradient-button">
            Create New Post
          </Button>
        </div>

        {isLoading ? (
          <div className="flex flex-col items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
            <p className="text-muted-foreground">Loading your posts...</p>
          </div>
        ) : posts.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12 border rounded-lg bg-muted/20">
            <div className="bg-primary/10 p-3 rounded-full mb-4">
              <ImageIcon className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-xl font-semibold mb-2">No posts yet</h3>
            <p className="text-muted-foreground text-center max-w-md mb-6">
              You haven't created any posts yet. Generate your first social media post to see it here.
            </p>
            <Button onClick={() => navigate("/create")}>Create Your First Post</Button>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {posts.map((post) => (
                <Card key={post.id} className="overflow-hidden flex flex-col h-full">
                  <CardHeader className="p-4 pb-0">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-base line-clamp-1">
                          {post.content.topic || "Untitled Post"}
                        </CardTitle>
                        <p className="text-xs text-muted-foreground mt-1">
                          {formatDate(post.createdAt)} • {post.content.platform}
                        </p>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleViewPost(post.id)}>
                            <ExternalLink className="h-4 w-4 mr-2" /> View Post
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleCopyCaption(post.id, post.caption)}>
                            <Copy className="h-4 w-4 mr-2" /> Copy Caption
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleCopyHashtags(post.id, post.caption)}>
                            <Hash className="h-4 w-4 mr-2" /> Copy Hashtags
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleDownloadImage(post.id)}>
                            <Download className="h-4 w-4 mr-2" /> Download Image
                          </DropdownMenuItem>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <DropdownMenuItem
                                className="text-destructive focus:text-destructive"
                                onSelect={(e) => e.preventDefault()}
                              >
                                <Trash2 className="h-4 w-4 mr-2" /> Delete Post
                              </DropdownMenuItem>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                <AlertDialogDescription>
                                  This action cannot be undone. This will permanently delete your post.
                                  <div className="mt-2 flex items-center text-amber-500 bg-amber-500/10 p-2 rounded">
                                    <AlertTriangle className="h-4 w-4 mr-2" />
                                    <span className="text-sm">
                                      Note: This will still count towards your monthly post limit.
                                    </span>
                                  </div>
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleDeletePost(post.id)}
                                  className="bg-destructive hover:bg-destructive/90"
                                  disabled={isDeleting === post.id}
                                >
                                  {isDeleting === post.id ? (
                                    <>
                                      <Loader2 className="h-4 w-4 mr-2 animate-spin" /> Deleting...
                                    </>
                                  ) : (
                                    <>Delete</>
                                  )}
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardHeader>
                  <CardContent className="p-4 flex-grow">
                    <div
                      className="rounded-md overflow-hidden border cursor-pointer"
                      onClick={() => handleViewPost(post.id)}
                    >
                      <DynamicAspectRatio
                      imageSize={post.content.imageSize}
                      imageData={post.imageData || ""}
                        className="bg-muted"
                      >
                      {post.imageData ? (
                      <img
                      src={post.imageData}
                      alt={post.content.topic || "Social media post"}
                      className="object-cover w-full h-full"
                      onError={(e) => {
                      (e.target as HTMLImageElement).src = "/placeholder-image.jpg";
                      console.error(`Failed to load image for post ${post.id}`);
                      }}
                        />
                      ) : post.imageUrl ? (
                      <div className="flex items-center justify-center h-full bg-muted relative">
                      <ImageIcon className="h-8 w-8 text-muted-foreground" />
                      <div className="absolute inset-0 flex items-center justify-center bg-black/60 text-white text-xs font-medium p-2 text-center">
                        Image loading...<br />Click to view full post
                        </div>
                        </div>
                      ) : (
                      <div className="flex items-center justify-center h-full bg-muted">
                        <ImageIcon className="h-8 w-8 text-muted-foreground" />
                        </div>
                         )}
                       </DynamicAspectRatio>
                    </div>
                    <div className="mt-3 space-y-2">
                      <div>
                        <p className="text-xs font-medium text-muted-foreground mb-1">Caption:</p>
                        <p className="text-sm line-clamp-2">
                          {post.caption ? post.caption.split("\n\n")[0] : "No caption available"}
                        </p>
                      </div>

                      {post.caption ? (
                        post.caption.includes("\n\n") ? (
                          <div>
                            <p className="text-xs font-medium text-muted-foreground mb-1">Hashtags:</p>
                            <p className="text-sm text-primary line-clamp-2">
                              {post.caption.split("\n\n")[1] || "No hashtags available"}
                            </p>
                          </div>
                        ) : (
                          <div>
                            <p className="text-xs font-medium text-muted-foreground mb-1">Hashtags:</p>
                            <p className="text-sm text-muted-foreground italic">No hashtags available</p>
                          </div>
                        )
                      ) : null}
                    </div>
                  </CardContent>
                  <CardFooter className="p-4 pt-0 flex justify-between">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleCopyCaption(post.id, post.caption)}
                      disabled={copyStatus[post.id]?.caption}
                    >
                      {copyStatus[post.id]?.caption ? (
                        <>Copied!</>
                      ) : (
                        <>
                          <Copy className="h-4 w-4 mr-2" /> Copy
                        </>
                      )}
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="destructive" size="sm">
                          <Trash2 className="h-4 w-4 mr-2" /> Delete
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                          <AlertDialogDescription>
                            This action cannot be undone. This will permanently delete your post.
                            <div className="mt-2 flex items-center text-amber-500 bg-amber-500/10 p-2 rounded">
                              <AlertTriangle className="h-4 w-4 mr-2" />
                              <span className="text-sm">
                                Note: This will still count towards your monthly post limit.
                              </span>
                            </div>
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleDeletePost(post.id)}
                            className="bg-destructive hover:bg-destructive/90"
                            disabled={isDeleting === post.id}
                          >
                            {isDeleting === post.id ? (
                              <>
                                <Loader2 className="h-4 w-4 mr-2 animate-spin" /> Deleting...
                              </>
                            ) : (
                              <>Delete</>
                            )}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </CardFooter>
                </Card>
              ))}
            </div>
            {pagination.pages > 1 && (
              <div className="flex justify-center mt-8">
                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious
                        onClick={() => handlePageChange(currentPage - 1)}
                        isActive={currentPage > 1}
                      />
                    </PaginationItem>
                    {Array.from({ length: pagination.pages }, (_, i) => (
                      <PaginationItem key={i}>
                        <PaginationLink
                          onClick={() => handlePageChange(i + 1)}
                          isActive={currentPage === i + 1}
                        >
                          {i + 1}
                        </PaginationLink>
                      </PaginationItem>
                    ))}
                    <PaginationItem>
                      <PaginationNext
                        onClick={() => handlePageChange(currentPage + 1)}
                        isActive={currentPage < pagination.pages}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
