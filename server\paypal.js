// Note: We're using direct PayPal API calls instead of SDK for better compatibility
import crypto from 'crypto';

// PayPal client configuration
const getPayPalEnvironment = () => {
  const environment = process.env.PAYPAL_ENVIRONMENT || 'sandbox';
  return environment === 'live' ? 'live' : 'sandbox';
};

const getPayPalConfig = () => {
  const environment = getPayPalEnvironment();

  return {
    clientId: process.env.PAYPAL_CLIENT_ID,
    clientSecret: process.env.PAYPAL_CLIENT_SECRET,
    environment: environment,
    // Base URL for PayPal API
    baseUrl: environment === 'live'
      ? 'https://api.paypal.com'
      : 'https://api.sandbox.paypal.com'
  };
};

// Get access token for PayPal API
export const getAccessToken = async () => {
  const config = getPayPalConfig();

  if (!config.clientId || !config.clientSecret) {
    throw new Error('PayPal credentials not configured');
  }

  const auth = Buffer.from(`${config.clientId}:${config.clientSecret}`).toString('base64');

  const response = await fetch(`${config.baseUrl}/v1/oauth2/token`, {
    method: 'POST',
    headers: {
      'Authorization': `Basic ${auth}`,
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: 'grant_type=client_credentials'
  });

  if (!response.ok) {
    throw new Error('Failed to get PayPal access token');
  }

  const data = await response.json();
  return data.access_token;
};

// Verify payment with PayPal
export const verifyPayPalPayment = async (orderID) => {
  try {
    const accessToken = await getAccessToken();
    const config = getPayPalConfig();

    const response = await fetch(`${config.baseUrl}/v2/checkout/orders/${orderID}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to verify payment with PayPal');
    }

    const orderDetails = await response.json();
    return orderDetails;
  } catch (error) {
    console.error('PayPal verification error:', error);
    throw error;
  }
};

// Get plan details based on plan name and billing cycle
export const getPlanDetails = (planName, billingCycle) => {
  const plans = {
    'basic': {
      monthly: { price: 5, posts_limit: 50 },
      yearly: { price: 4, posts_limit: 50 }
    },
    'pro': {
      monthly: { price: 15, posts_limit: 200 },
      yearly: { price: 12, posts_limit: 200 }
    },
    'ultra': {
      monthly: { price: 30, posts_limit: 500 },
      yearly: { price: 25, posts_limit: 500 }
    }
  };

  const planKey = planName.toLowerCase();
  if (!plans[planKey]) {
    throw new Error(`Invalid plan: ${planName}`);
  }

  return plans[planKey][billingCycle];
};

// Get subscription plan details from environment variables
export const getSubscriptionPlanDetails = (planName, billingCycle) => {
  const planKey = planName.toUpperCase();
  const cycleKey = billingCycle.toUpperCase();

  const priceKey = `PAYPAL_${planKey}_${cycleKey}_PRICE`;
  const intervalKey = `PAYPAL_${planKey}_${cycleKey}_INTERVAL`;
  const planIdKey = `PAYPAL_${planKey}_${cycleKey}_PLAN_ID`;

  const price = process.env[priceKey];
  const interval = process.env[intervalKey];
  const planId = process.env[planIdKey];

  if (!price || !interval) {
    throw new Error(`Subscription configuration not found for ${planName} ${billingCycle}`);
  }

  if (!planId) {
    throw new Error(`PayPal plan ID not found for ${planName} ${billingCycle}. Please add ${planIdKey} to your environment variables.`);
  }

  // Parse interval (e.g., "1MONTH" -> frequency: "MONTH", frequency_interval: "1")
  const intervalMatch = interval.match(/^(\d+)(DAY|WEEK|MONTH|YEAR)$/);
  if (!intervalMatch) {
    throw new Error(`Invalid interval format: ${interval}`);
  }

  const [, frequency_interval, frequency] = intervalMatch;

  return {
    price: parseFloat(price),
    frequency: frequency,
    frequency_interval: parseInt(frequency_interval),
    posts_limit: getPlanDetails(planName, billingCycle).posts_limit,
    plan_id: planId
  };
};

// Calculate next billing date
export const getNextBillingDate = (billingCycle) => {
  const now = new Date();
  if (billingCycle === 'yearly') {
    return new Date(now.getFullYear() + 1, now.getMonth(), now.getDate());
  } else {
    return new Date(now.getFullYear(), now.getMonth() + 1, now.getDate());
  }
};

// Verify PayPal webhook signature
export const verifyWebhookSignature = (headers, body, webhookId) => {
  try {
    const authAlgo = headers['paypal-auth-algo'];
    const transmission_id = headers['paypal-transmission-id'];
    const cert_id = headers['paypal-cert-id'];
    const transmission_time = headers['paypal-transmission-time'];
    const webhook_signature = headers['paypal-transmission-sig'];

    if (!authAlgo || !transmission_id || !cert_id || !transmission_time || !webhook_signature) {
      console.error('Missing required webhook headers');
      return false;
    }

    // For now, we'll do basic validation. In production, you should verify the certificate
    // and signature properly using PayPal's webhook verification API
    console.log('Webhook signature verification - basic validation passed');
    return true;
  } catch (error) {
    console.error('Webhook signature verification failed:', error);
    return false;
  }
};

// Get subscription details from PayPal
export const getSubscriptionDetails = async (subscriptionId) => {
  try {
    const accessToken = await getAccessToken();
    const config = getPayPalConfig();

    const response = await fetch(`${config.baseUrl}/v1/billing/subscriptions/${subscriptionId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`Failed to get subscription details: ${JSON.stringify(error)}`);
    }

    const subscriptionDetails = await response.json();
    return subscriptionDetails;
  } catch (error) {
    console.error('Error getting subscription details:', error);
    throw error;
  }
};

// Check if subscription is active with PayPal
export const isSubscriptionActive = async (subscriptionId) => {
  try {
    const details = await getSubscriptionDetails(subscriptionId);
    return details.status === 'ACTIVE';
  } catch (error) {
    console.error('Error checking subscription status:', error);
    return false;
  }
};