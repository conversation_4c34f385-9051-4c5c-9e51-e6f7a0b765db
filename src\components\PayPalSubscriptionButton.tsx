import React from 'react';
import { PayPalButtons, usePayPalScriptReducer } from '@paypal/react-paypal-js';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { usePayPal } from '@/hooks/usePayPal';

interface PayPalSubscriptionButtonProps {
  planName: string;
  billingCycle: 'monthly' | 'yearly';
  onSuccess?: (details: any) => void;
  onError?: (error: any) => void;
  className?: string;
}

export default function PayPalSubscriptionButton({ 
  planName, 
  billingCycle, 
  onSuccess, 
  onError,
  className 
}: PayPalSubscriptionButtonProps) {
  const { config, loading: configLoading, error: configError } = usePayPal();
  const { toast } = useToast();

  // If PayPal config is not available, show fallback button
  if (configLoading || configError || !config) {
    return (
      <Button className={className} disabled>
        {configLoading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Loading PayPal...
          </>
        ) : (
          'PayPal Unavailable'
        )}
      </Button>
    );
  }

  return <PayPalSubscriptionButtonInner {...{ planName, billingCycle, onSuccess, onError, className }} />;
}

function PayPalSubscriptionButtonInner({ 
  planName, 
  billingCycle, 
  onSuccess, 
  onError,
  className 
}: PayPalSubscriptionButtonProps) {
  const [{ isLoading, isResolved }] = usePayPalScriptReducer();
  const { toast } = useToast();

  const createSubscription = async (data: any, actions: any) => {
    try {
      // Get subscription plan configuration from backend
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/payments/paypal/subscription-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          planName,
          billingCycle
        })
      });

      if (!response.ok) {
        throw new Error('Failed to get subscription configuration');
      }

      const config = await response.json();
      console.log('Subscription config:', config);
      console.log('Plan ID:', config.plan_id);

      if (!config.plan_id) {
        throw new Error(`No plan_id received from server. Config: ${JSON.stringify(config)}`);
      }

      // Create subscription with plan ID
      return actions.subscription.create({
        'plan_id': config.plan_id
      });
    } catch (error) {
      console.error('Error creating subscription:', error);
      throw error;
    }
  };

  const onApprove = async (data: any, actions: any) => {
    try {
      console.log('PayPal subscription approved:', data);
      
      // Get fresh token from localStorage
      let token = localStorage.getItem('auth_token');
      console.log('Token exists:', !!token);
      
      if (!token) {
        throw new Error('Please log in again to complete your subscription');
      }
      
      console.log('Sending subscription activation to backend...');
      
      const response = await fetch('/api/payments/paypal/subscription-activate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          subscriptionID: data.subscriptionID,
          planName,
          billingCycle
        })
      });

      console.log('Backend response status:', response.status);

      if (response.status === 401 || response.status === 403) {
        // Token is invalid - clear it and ask user to log in again
        localStorage.removeItem('auth_token');
        throw new Error('Your session has expired. Please log in again to complete your subscription.');
      }

      if (response.ok) {
        const result = await response.json();
        console.log('Subscription activation successful:', result);
        toast({
          title: "Subscription started!",
          description: `Welcome to ${planName} plan! You're now subscribed with ${billingCycle} billing.`,
        });
        onSuccess?.(result);
      } else {
        const error = await response.json();
        console.error('Backend activation failed:', error);
        throw new Error(error.error || error.message || 'Subscription activation failed');
      }
    } catch (error) {
      console.error('Subscription error:', error);
      toast({
        title: "Subscription Issue",
        description: error instanceof Error ? error.message : "Something went wrong with your subscription.",
        variant: "destructive",
      });
      onError?.(error);
    }
  };

  const onErrorHandler = (error: any) => {
    console.error('PayPal subscription error:', error);
    toast({
      title: "Subscription error",
      description: "There was an issue processing your subscription. Please try again.",
      variant: "destructive",
    });
    onError?.(error);
  };

  if (isLoading) {
    return (
      <Button className={className} disabled>
        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        Loading PayPal...
      </Button>
    );
  }

  if (!isResolved) {
    return (
      <Button className={className} disabled>
        PayPal Unavailable
      </Button>
    );
  }

  return (
    <div className="w-full">
      <PayPalButtons
        style={{
          layout: 'vertical',
          color: 'gold',
          shape: 'rect',
          label: 'subscribe'
        }}
        createSubscription={createSubscription}
        onApprove={onApprove}
        onError={onErrorHandler}
        onCancel={() => {
          toast({
            title: "Subscription cancelled",
            description: "You can start your subscription anytime.",
          });
        }}
      />
    </div>
  );
}