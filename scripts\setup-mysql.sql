-- MySQL Database Setup Script for SocialSparkAI
-- Run this script to set up the MySQL database

-- Create the database (replace 'socialspark' with your preferred database name)
CREATE DATABASE IF NOT EXISTS socialspark CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Use the database
USE socialspark;

-- Create profiles table
CREATE TABLE IF NOT EXISTS profiles (
  id VARCHAR(36) PRIMARY KEY,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  full_name VARCHAR(255),
  plan ENUM('free', 'basic', 'pro', 'ultra') DEFAULT 'free',
  posts_count INT DEFAULT 0,
  posts_limit INT DEFAULT 3,
  last_post_time TIMESTAMP NULL,
  subscription_status ENUM('active', 'cancelled', 'expired') DEFAULT 'active',
  billing_cycle ENUM('monthly', 'yearly') NULL,
  next_billing_date TIMESTAMP NULL,
  paypal_order_id VARCHAR(255) NULL,
  paypal_subscription_id VARCHAR(255) NULL,
  is_admin BOOLEAN DEFAULT FALSE
);

-- Create posts table
CREATE TABLE IF NOT EXISTS posts (
  id VARCHAR(36) PRIMARY KEY,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  user_id VARCHAR(36) NOT NULL,
  content TEXT NOT NULL,
  caption TEXT,
  image_url TEXT,
  published BOOLEAN DEFAULT FALSE,
  FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE
);

-- Create users table for basic authentication
CREATE TABLE IF NOT EXISTS users (
  id VARCHAR(36) PRIMARY KEY,
  email VARCHAR(255) NOT NULL UNIQUE,
  password_hash VARCHAR(255) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (id) REFERENCES profiles(id) ON DELETE CASCADE
);

-- Create analytics tables
CREATE TABLE IF NOT EXISTS analytics_events (
  id VARCHAR(36) PRIMARY KEY,
  event_type VARCHAR(100) NOT NULL,
  user_id VARCHAR(36),
  session_id VARCHAR(36),
  ip_address VARCHAR(45),
  user_agent TEXT,
  event_data JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_event_type (event_type),
  INDEX idx_user_id (user_id),
  INDEX idx_created_at (created_at)
);

CREATE TABLE IF NOT EXISTS user_sessions (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  session_token VARCHAR(255) NOT NULL,
  last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  ip_address VARCHAR(45),
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY unique_user_session (user_id, session_token),
  INDEX idx_user_id (user_id),
  INDEX idx_session_token (session_token),
  INDEX idx_last_activity (last_activity)
);

-- Create indexes for better performance (ignore errors if they already exist)
CREATE INDEX idx_posts_user_id ON posts(user_id);
CREATE INDEX idx_users_email ON users(email);

-- Display success message
SELECT 'MySQL database setup completed successfully!' AS message;

-- Show table structure
SHOW TABLES;
