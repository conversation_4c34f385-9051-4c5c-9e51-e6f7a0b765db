import { verifyToken, getUserProfile } from './auth.js';

// Admin authentication middleware
export const authenticateAdmin = async (req, res, next) => {
  try {
    // First check for valid authentication token
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return res.status(401).json({ error: 'Authentication token required' });
    }

    const user = verifyToken(token);
    if (!user) {
      return res.status(403).json({ error: 'Invalid or expired token' });
    }

    // Get user profile to check admin status
    const profileResult = await getUserProfile(user.sub);
    console.log('authenticateAdmin - Profile result:', profileResult);
    
    if (profileResult.error) {
      console.log('authenticateAdmin - Profile error:', profileResult.error);
      return res.status(404).json({ error: 'User profile not found' });
    }

    console.log('authenticateAdmin - Admin status:', profileResult.data.is_admin);
    
    // Check if user is admin
    if (!(profileResult.data.is_admin === 1 || profileResult.data.is_admin === "1" || profileResult.data.is_admin === true)) {
      console.log('authenticateAdmin - Not admin, access denied');
      return res.status(403).json({ error: 'Admin access required' });
    }

    console.log('authenticateAdmin - Admin access granted');

    req.user = user;
    req.adminProfile = profileResult.data;
    next();
  } catch (error) {
    console.error('Admin authentication error:', error);
    res.status(500).json({ error: 'Authentication failed' });
  }
};

// Check if user is admin (for frontend use)
export const checkAdminStatus = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    req.isAdmin = false;
    
    if (token) {
      const user = verifyToken(token);
      if (user) {
        const profileResult = await getUserProfile(user.sub);
        console.log('Admin status check - profile data:', profileResult.data);
        console.log('Admin status check - is_admin value:', profileResult.data?.is_admin, 'type:', typeof profileResult.data?.is_admin);
        if (!profileResult.error && (profileResult.data.is_admin === 1 || profileResult.data.is_admin === "1" || profileResult.data.is_admin === true)) {
          req.isAdmin = true;
          req.user = user;
          req.adminProfile = profileResult.data;
          console.log('Admin status check - Setting isAdmin to true');
        } else {
          console.log('Admin status check - Not setting admin, is_admin:', profileResult.data?.is_admin);
        }
      }
    }
    
    next();
  } catch (error) {
    console.error('Admin status check error:', error);
    req.isAdmin = false;
    next();
  }
};