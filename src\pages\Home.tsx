
import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Link, useNavigate } from "react-router-dom"
import { ArrowRight, Image, Zap, Check, Heart, MessageCircle, Bookmark, Share2 } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { useAuth } from "@/context/AuthContext"

export default function Home() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { incrementPostsCount, checkPostsLimit, profile, user, loading, refreshProfile } = useAuth();
  const [platform, setPlatform] = useState("Instagram");
  const [tone, setTone] = useState("Professional");
  const [topic, setTopic] = useState("");
  const [hasRefreshedOnMount, setHasRefreshedOnMount] = useState(false);

  // Refresh profile data when Home page loads to ensure latest plan info
  useEffect(() => {
    if (user && !loading && !hasRefreshedOnMount) {
      console.log('Home: Refreshing profile to ensure latest plan data');
      setHasRefreshedOnMount(true);
      refreshProfile().then(({ success, error }) => {
        if (success) {
          console.log('Home: Profile refreshed successfully');
        } else {
          console.error('Home: Failed to refresh profile:', error);
        }
      });
    }
  }, [user, loading, refreshProfile, hasRefreshedOnMount]);

  // Check if profile needs refresh (if name contains [PLAN UPDATED]) - only once
  useEffect(() => {
    if (profile && profile.full_name && profile.full_name.includes('[PLAN UPDATED]')) {
      console.log('Home: Detected plan update notification, showing toast only');

      // Show toast but don't refresh again to prevent loops
      toast({
        title: "Plan Updated",
        description: "Your subscription plan has been updated",
      });
    }
  }, [profile, toast]);

  // Check if user has reached posts limit
  const hasReachedPostsLimit = !checkPostsLimit();

  const handleGeneratePost = () => {
    if (!topic) {
      toast({
        title: "Topic required",
        description: "Please enter a topic for your post",
        variant: "destructive"
      });
      return;
    }

    // Check if user is logged in
    if (!user) {
      toast({
        title: "Login required",
        description: "Please log in to generate posts",
        variant: "destructive"
      });
      navigate("/login", { state: { redirectTo: "/" } });
      return;
    }

    // Navigate to create page with the form values to pre-fill Platform, Tone, and Topic
    navigate('/create', {
      state: {
        platform,
        tone,
        topic
      }
    });
  };
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="section-padding bg-gradient-to-br from-background to-accent/10">
        <div className="max-w-7xl mx-auto flex flex-col md:flex-row items-center justify-between gap-12">
          <div className="flex-1 text-center md:text-left space-y-6">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight animate-fade-in">
              Spark Your Social Presence with AI-Powered Posts
            </h1>
            <p className="text-lg md:text-xl text-muted-foreground max-w-xl mx-auto md:mx-0">
              Generate engaging captions and visuals in seconds—no creative block, no fuss.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center md:justify-start">
              <Button size="lg" className="gradient-button text-lg px-8">
                Try It Free
              </Button>
              <Button size="lg" variant="outline" className="gap-2" asChild>
                <Link to="/examples">
                  View Examples <ArrowRight className="h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
          <div className="flex-1 w-full max-w-md">
            <div className="relative bg-card rounded-lg border shadow-2xl overflow-hidden p-6">
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-spark-indigo/20 to-spark-violet/30 blur-3xl"></div>
              <div className="space-y-4 relative">
                <div className="flex items-center gap-2 mb-6">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <div className="ml-2 text-xs text-muted-foreground">SocialSparkGen Generator</div>
                </div>
                <div className="space-y-4">
                  <div className="bg-background p-3 rounded-md shadow-sm">
                    <p className="text-sm font-medium">Platform</p>
                    <select
                      className="mt-1 w-full bg-transparent border rounded p-2 text-sm"
                      value={platform}
                      onChange={(e) => setPlatform(e.target.value)}
                    >
                      <option value="Instagram">Instagram</option>
                      <option value="X">X (Twitter)</option>
                      <option value="LinkedIn">LinkedIn</option>
                      <option value="Facebook">Facebook</option>
                    </select>
                  </div>
                  <div className="bg-background p-3 rounded-md shadow-sm">
                    <p className="text-sm font-medium">Tone</p>
                    <select
                      className="mt-1 w-full bg-transparent border rounded p-2 text-sm"
                      value={tone}
                      onChange={(e) => setTone(e.target.value)}
                    >
                      <option value="Professional">Professional</option>
                      <option value="Casual">Casual</option>
                      <option value="Enthusiastic">Enthusiastic</option>
                      <option value="Informative">Informative</option>
                    </select>
                  </div>
                  <div className="bg-background p-3 rounded-md shadow-sm">
                    <p className="text-sm font-medium">Topic</p>
                    <input
                      className="mt-1 w-full bg-transparent border rounded p-2 text-sm"
                      placeholder="Coffee shop opening"
                      value={topic}
                      onChange={(e) => setTopic(e.target.value)}
                    />
                  </div>
                  {user && hasReachedPostsLimit && profile && (
                    <div className="bg-destructive/10 text-destructive p-2 rounded-md text-xs mb-2 flex items-start gap-2">
                      <div className="mt-0.5">⚠️</div>
                      <div>
                        You've used all {profile.posts_limit} posts for your {profile.plan} plan this month.
                        <Link to="/pricing" className="block mt-1 text-primary font-medium">Upgrade your plan →</Link>
                      </div>
                    </div>
                  )}
                  <div className="flex gap-2">
                    <Button
                      className="w-full gradient-button gap-2"
                      onClick={handleGeneratePost}
                    >
                      <Zap className="h-4 w-4" /> Generate Post
                    </Button>
                    <Button variant="outline" asChild className="whitespace-nowrap">
                      <Link to="/create">Advanced</Link>
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Highlight Section */}
      <section className="section-padding">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Create Scroll-Stopping Content in Seconds
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Save hours of creative effort with our AI-powered social media content generator that perfectly matches your brand voice.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                title: "Multi-Platform Support",
                description: "Optimized templates for Twitter, Instagram, LinkedIn & Facebook",
                icon: <Image className="h-10 w-10 text-spark-indigo" />,
              },
              {
                title: "Brand Voice Matching",
                description: "AI that adapts to your unique tone and style preferences",
                icon: <Zap className="h-10 w-10 text-spark-indigo" />,
              },
              {
                title: "Image-Caption Pairing",
                description: "Perfect visual matches for your written content",
                icon: <Image className="h-10 w-10 text-spark-indigo" />,
              },
            ].map((feature, i) => (
              <div key={i} className="bg-card rounded-xl p-6 border shadow-sm card-hover">
                <div className="bg-primary/10 w-16 h-16 rounded-lg flex items-center justify-center mb-4">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                <p className="text-muted-foreground">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="section-padding bg-gradient-to-br from-background to-accent/10">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Why Creators Choose SocialSparkGen
              </h2>
              <div className="space-y-4">
                {[
                  "Save 5+ hours per week on social media content creation",
                  "Maintain consistent posting schedule without burnout",
                  "Generate ideas when you're stuck in a creative rut",
                  "Scale your content across multiple platforms effortlessly",
                  "Always on-brand voice that resonates with your audience",
                ].map((item, i) => (
                  <div key={i} className="flex gap-3">
                    <div className="shrink-0 mt-1">
                      <Check className="h-5 w-5 text-spark-indigo" />
                    </div>
                    <p>{item}</p>
                  </div>
                ))}
              </div>
              <Button size="lg" className="gradient-button mt-8 gap-2">
                Get Started <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
            <div className="bg-card rounded-xl overflow-hidden border shadow-lg">
              <div className="p-6 space-y-4">
                {/* Example Post Preview */}
                <div className="bg-background rounded-lg shadow-sm overflow-hidden">
                  <div className="p-4">
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 bg-spark-indigo rounded-full"></div>
                      <div>
                      <p className="font-medium text-sm">Coffee Culture</p>
                      </div>
                    </div>
                    <p className="mt-3 text-sm">✨ Exciting News! Our new downtown location opens THIS FRIDAY! Be one of the first 50 customers and get a free specialty drink of your choice. We can't wait to serve you our award-winning brews in our cozy new space. <span className="text-primary">#CoffeeCulture #GrandOpening #DowntownCoffee</span></p>
                  </div>
                  <div className="rounded overflow-hidden">
                    <img
                      src="/coffe_example.png"
                      alt="Coffee shop post"
                      className="w-full h-auto object-cover"
                    />
                  </div>
                </div>
                <div className="flex justify-between text-sm text-muted-foreground">
                  <p>Text generated in 1.8s + image in 6.7s</p>
                  <div className="flex gap-1">
                    <div className="w-2 h-2 bg-spark-indigo rounded-full mt-1"></div>
                    <div className="w-2 h-2 bg-spark-indigo rounded-full mt-1"></div>
                    <div className="w-2 h-2 bg-spark-indigo rounded-full mt-1"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section-padding bg-primary">
        <div className="max-w-7xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-primary-foreground">
            Ready to Transform Your Social Media Strategy?
          </h2>
          <p className="text-lg md:text-xl text-primary-foreground/80 max-w-2xl mx-auto mb-8">
            Join thousands of creators and businesses saving time while creating better content.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="secondary" className="text-primary font-medium px-8">
              Try It Free
            </Button>
            <Button
              size="lg"
              variant="secondary"
              className="border-white font-medium bg-white/20 text-white hover:bg-white/30 hover:text-white"
              asChild
            >
              <Link to="/pricing">View Pricing</Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}
