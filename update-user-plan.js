#!/usr/bin/env node

/**
 * <PERSON>ript to update a user's plan
 *
 * Usage:
 *   node update-user-plan.js <email> <plan>
 *
 * Example:
 *   node update-user-plan.js <EMAIL> pro
 */

// Import required modules
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';
import Database from 'better-sqlite3';
import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get the directory of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Define valid plans and their limits
const VALID_PLANS = {
  free: { limit: 3, rateLimit: 1 },
  basic: { limit: 50, rateLimit: 1 },
  pro: { limit: 200, rateLimit: 3 },
  ultra: { limit: 500, rateLimit: 5 }
};

// Parse command line arguments
const [,, email, plan, forceLogout] = process.argv;

// Validate arguments
if (!email || !plan) {
  console.error('Error: Email and plan are required');
  console.log('Usage: node update-user-plan.js <email> <plan> [force-logout]');
  console.log('Valid plans: free, basic, pro, ultra');
  console.log('Add "force-logout" as the third argument to invalidate the user\'s session');
  process.exit(1);
}

// Validate plan
if (!Object.keys(VALID_PLANS).includes(plan)) {
  console.error(`Error: Invalid plan "${plan}"`);
  console.log('Valid plans: free, basic, pro, ultra');
  process.exit(1);
}

// Check if force logout is requested
const shouldForceLogout = forceLogout === 'force-logout';

// Determine which database to use (SQLite or MySQL)
const useMySQL = process.env.DB_HOST && process.env.DB_USER && process.env.DB_PASSWORD;

// Function to update user plan in SQLite
async function updateUserPlanSQLite() {
  try {
    // Define data directory
    const dataDir = join(__dirname, 'data');

    // Create data directory if it doesn't exist
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    // Database file path
    const dbPath = join(dataDir, 'socialspark.db');

    // Check if database file exists
    if (!fs.existsSync(dbPath)) {
      console.error(`Error: Database file not found at ${dbPath}`);
      process.exit(1);
    }

    // Connect to the database
    const db = new Database(dbPath);

    // Enable foreign keys
    db.pragma('foreign_keys = ON');

    // Find the user by email
    const user = db.prepare('SELECT * FROM users WHERE email = ?').get(email);

    if (!user) {
      console.error(`Error: User with email "${email}" not found`);
      process.exit(1);
    }

    // Update the user's plan and posts_limit
    const updateStmt = db.prepare(`
      UPDATE profiles
      SET plan = ?, posts_limit = ?
      WHERE id = ?
    `);

    const result = updateStmt.run(plan, VALID_PLANS[plan].limit, user.id);

    if (result.changes === 0) {
      console.error(`Error: Failed to update plan for user "${email}"`);
      process.exit(1);
    }

    // Get the updated profile
    const profile = db.prepare('SELECT * FROM profiles WHERE id = ?').get(user.id);

    console.log(`Successfully updated user "${email}" to ${plan} plan`);
    console.log('Updated profile:', {
      id: profile.id,
      full_name: profile.full_name,
      plan: profile.plan,
      posts_count: profile.posts_count,
      posts_limit: profile.posts_limit
    });

    // If force logout is requested, update the user's JWT secret to invalidate all sessions
    if (shouldForceLogout) {
      console.log(`Force logout requested for user "${email}"`);

      // Generate a new random string to append to the user ID
      const randomString = crypto.randomBytes(8).toString('hex');

      // Update the user's ID in the database (this will invalidate all JWT tokens)
      // Note: We're not actually changing the ID, just adding a note that the user should refresh
      const updateNoteStmt = db.prepare(`
        UPDATE profiles
        SET full_name = ?
        WHERE id = ?
      `);

      // Add a note to the user's name to indicate they need to refresh
      const currentName = profile.full_name || '';
      const newName = currentName.includes('[PLAN UPDATED]')
        ? currentName
        : `${currentName} [PLAN UPDATED]`;

      updateNoteStmt.run(newName, profile.id);

      console.log(`Added note to user's profile. User should refresh their session.`);
    }

    // Close the database connection
    db.close();
  } catch (error) {
    console.error('Error updating user plan:', error);
    process.exit(1);
  }
}

// Function to update user plan in MySQL
async function updateUserPlanMySQL() {
  let connection;

  try {
    // Create MySQL connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME || 'defaultdb'
    });

    // Find the user by email
    const [users] = await connection.query(
      'SELECT * FROM users WHERE email = ?',
      [email]
    );

    if (users.length === 0) {
      console.error(`Error: User with email "${email}" not found`);
      process.exit(1);
    }

    const user = users[0];

    // Update the user's plan and posts_limit
    const [result] = await connection.query(
      'UPDATE profiles SET plan = ?, posts_limit = ? WHERE id = ?',
      [plan, VALID_PLANS[plan].limit, user.id]
    );

    if (result.affectedRows === 0) {
      console.error(`Error: Failed to update plan for user "${email}"`);
      process.exit(1);
    }

    // Get the updated profile
    const [profiles] = await connection.query(
      'SELECT * FROM profiles WHERE id = ?',
      [user.id]
    );

    const profile = profiles[0];

    console.log(`Successfully updated user "${email}" to ${plan} plan`);
    console.log('Updated profile:', {
      id: profile.id,
      full_name: profile.full_name,
      plan: profile.plan,
      posts_count: profile.posts_count,
      posts_limit: profile.posts_limit
    });

    // If force logout is requested, update the user's name to indicate they need to refresh
    if (shouldForceLogout) {
      console.log(`Force logout requested for user "${email}"`);

      // Add a note to the user's name to indicate they need to refresh
      const currentName = profile.full_name || '';
      const newName = currentName.includes('[PLAN UPDATED]')
        ? currentName
        : `${currentName} [PLAN UPDATED]`;

      // Update the user's name in the database
      await connection.query(
        'UPDATE profiles SET full_name = ? WHERE id = ?',
        [newName, profile.id]
      );

      console.log(`Added note to user's profile. User should refresh their session.`);
    }
  } catch (error) {
    console.error('Error updating user plan:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Execute the appropriate function based on database type
if (useMySQL) {
  updateUserPlanMySQL();
} else {
  updateUserPlanSQLite();
}
