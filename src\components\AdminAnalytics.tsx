import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { Users, TrendingUp, DollarSign, Activity, Eye, Calendar } from 'lucide-react';

interface OverviewStats {
  totalUsers: number;
  totalPosts: number;
  planDistribution: Array<{ plan: string; count: number }>;
  activeSubscriptions: number;
  postsToday: number;
  newUsersToday: number;
  postsInLastMinute: number;
  globalRateLimits: {
    free: number;
    basic: number;
  };
}

interface UserGrowthData {
  userGrowth: Array<{ period: string; new_users: number }>;
  period: string;
  groupBy: string;
}

interface PostActivityData {
  postActivity: Array<{ period: string; posts_created: number }>;
  postsByPlan: Array<{ plan: string; post_count: number }>;
  period: string;
  groupBy: string;
}

interface RevenueData {
  revenueData: Array<{
    plan: string;
    billing_cycle: string;
    period: string;
    subscription_count: number;
    estimated_revenue: number;
  }>;
  totalRevenueByPlan: Array<{
    plan: string;
    billing_cycle: string;
    active_subscriptions: number;
    estimated_monthly_revenue: number;
  }>;
  period: string;
  groupBy: string;
  planPricing: any;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

export default function AdminAnalytics() {
  const [overviewStats, setOverviewStats] = useState<OverviewStats | null>(null);
  const [onlineUsers, setOnlineUsers] = useState<number>(0);
  const [userGrowthData, setUserGrowthData] = useState<UserGrowthData | null>(null);
  const [postActivityData, setPostActivityData] = useState<PostActivityData | null>(null);
  const [revenueData, setRevenueData] = useState<RevenueData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'reconnecting'>('connected');
  const [period, setPeriod] = useState('30');
  const [groupBy, setGroupBy] = useState('day');

  // Generic fetch wrapper with error handling
  const fetchWithRetry = async (url: string, retryCount = 0): Promise<any> => {
    if (retryCount > 0) {
      setConnectionStatus('reconnecting');
    }

    try {
      const token = localStorage.getItem('auth_token');
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await fetch(url, {
        headers: { 'Authorization': `Bearer ${token}` },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        setConnectionStatus('connected');
        return await response.json();
      } else if (response.status >= 500 && retryCount < 2) {
        // Retry on server errors
        setConnectionStatus('reconnecting');
        await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
        return fetchWithRetry(url, retryCount + 1);
      } else {
        setConnectionStatus('disconnected');
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error: any) {
      if (error.name === 'AbortError') {
        setConnectionStatus('disconnected');
        throw new Error('Request timeout');
      } else if (retryCount < 2 && (error.code === 'ECONNRESET' || error.message.includes('fetch') || error.message.includes('NetworkError'))) {
        // Retry on connection errors
        setConnectionStatus('reconnecting');
        await new Promise(resolve => setTimeout(resolve, 2000 * (retryCount + 1)));
        return fetchWithRetry(url, retryCount + 1);
      }
      setConnectionStatus('disconnected');
      throw error;
    }
  };

  const fetchOverviewStats = async () => {
    try {
      const data = await fetchWithRetry('/api/admin/stats/overview');
      setOverviewStats(data);
      setError(null);
    } catch (error: any) {
      console.error('Error fetching overview stats:', error);
      setError('Failed to load overview statistics');
    }
  };

  const fetchOnlineUsers = async () => {
    try {
      const data = await fetchWithRetry('/api/admin/stats/users-online');
      setOnlineUsers(data.count);
      setError(null);
    } catch (error: any) {
      console.error('Error fetching online users:', error);
      // Don't set error for online users as it's not critical
    }
  };

  const fetchUserGrowth = async () => {
    try {
      const data = await fetchWithRetry(`/api/admin/stats/user-growth?period=${period}&groupBy=${groupBy}`);
      setUserGrowthData(data);
      setError(null);
    } catch (error: any) {
      console.error('Error fetching user growth:', error);
      setError('Failed to load user growth data');
    }
  };

  const fetchPostActivity = async () => {
    try {
      const data = await fetchWithRetry(`/api/admin/stats/post-activity?period=${period}&groupBy=${groupBy}`);
      setPostActivityData(data);
      setError(null);
    } catch (error: any) {
      console.error('Error fetching post activity:', error);
      setError('Failed to load post activity data');
    }
  };

  const fetchRevenue = async () => {
    try {
      const data = await fetchWithRetry(`/api/admin/stats/revenue?period=${period}&groupBy=${groupBy}`);
      setRevenueData(data);
      setError(null);
    } catch (error: any) {
      console.error('Error fetching revenue:', error);
      setError('Failed to load revenue data');
    }
  };

  useEffect(() => {
    const fetchAllData = async () => {
      setLoading(true);
      await Promise.all([
        fetchOverviewStats(),
        fetchOnlineUsers(),
        fetchUserGrowth(),
        fetchPostActivity(),
        fetchRevenue()
      ]);
      setLoading(false);
    };

    fetchAllData();
  }, [period, groupBy]);

  // Auto-refresh online users and overview stats every 30 seconds with error handling
  useEffect(() => {
    const interval = setInterval(() => {
      // Only fetch if the component is still mounted and visible
      if (document.visibilityState === 'visible') {
        fetchOnlineUsers();
        fetchOverviewStats(); // Also refresh overview stats to get updated posts in last minute
      }
    }, 30000);

    // Also listen for visibility changes to pause/resume updates
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        fetchOnlineUsers();
        fetchOverviewStats();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      clearInterval(interval);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading analytics...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-64 space-y-4">
        <div className="text-lg text-red-600">⚠️ {error}</div>
        <button
          onClick={() => {
            setError(null);
            setLoading(true);
            const fetchAllData = async () => {
              await Promise.all([
                fetchOverviewStats(),
                fetchOnlineUsers(),
                fetchUserGrowth(),
                fetchPostActivity(),
                fetchRevenue()
              ]);
              setLoading(false);
            };
            fetchAllData();
          }}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Retry
        </button>
      </div>
    );
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'free': return '#94a3b8';
      case 'basic': return '#0088FE';
      case 'pro': return '#00C49F';
      case 'ultra': return '#FFBB28';
      default: return '#8884D8';
    }
  };

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overviewStats?.totalUsers || 0}</div>
            <p className="text-xs text-muted-foreground">
              +{overviewStats?.newUsersToday || 0} today
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Online Users</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{onlineUsers}</div>
            <p className="text-xs text-muted-foreground">
              Currently active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Posts</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overviewStats?.totalPosts || 0}</div>
            <p className="text-xs text-muted-foreground">
              +{overviewStats?.postsToday || 0} today
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Subscriptions</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overviewStats?.activeSubscriptions || 0}</div>
            <p className="text-xs text-muted-foreground">
              Paid subscribers
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Posts/Minute</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${
              overviewStats?.postsInLastMinute >= overviewStats?.globalRateLimits?.basic ? 'text-red-600' :
              overviewStats?.postsInLastMinute >= overviewStats?.globalRateLimits?.free ? 'text-yellow-600' :
              'text-green-600'
            }`}>
              {overviewStats?.postsInLastMinute || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Limits: {overviewStats?.globalRateLimits?.free || 30}(FREE) / {overviewStats?.globalRateLimits?.basic || 65}(BASIC)
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Connection Status */}
      <div className="flex gap-4 items-center justify-between">
        <div className="flex gap-4 items-center">
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            <Select value={period} onValueChange={setPeriod}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7">Last 7 days</SelectItem>
                <SelectItem value="30">Last 30 days</SelectItem>
                <SelectItem value="90">Last 90 days</SelectItem>
                <SelectItem value="365">Last year</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Select value={groupBy} onValueChange={setGroupBy}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="hour">By Hour</SelectItem>
              <SelectItem value="day">By Day</SelectItem>
              <SelectItem value="week">By Week</SelectItem>
              <SelectItem value="month">By Month</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Connection Status Indicator */}
        <div className="flex items-center gap-2 text-sm">
          <div className={`w-2 h-2 rounded-full ${
            connectionStatus === 'connected' ? 'bg-green-500' :
            connectionStatus === 'reconnecting' ? 'bg-yellow-500 animate-pulse' :
            'bg-red-500'
          }`} />
          <span className="text-muted-foreground">
            {connectionStatus === 'connected' ? 'Connected' :
             connectionStatus === 'reconnecting' ? 'Reconnecting...' :
             'Disconnected'}
          </span>
        </div>
      </div>

      {/* Analytics Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="users">User Growth</TabsTrigger>
          <TabsTrigger value="posts">Post Activity</TabsTrigger>
          <TabsTrigger value="revenue">Revenue</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Plan Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Plan Distribution</CardTitle>
                <CardDescription>Users by subscription plan</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={overviewStats?.planDistribution || []}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ plan, count, percent }) => `${plan}: ${count} (${(percent * 100).toFixed(0)}%)`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                    >
                      {overviewStats?.planDistribution?.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={getPlanColor(entry.plan)} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Posts by Plan */}
            <Card>
              <CardHeader>
                <CardTitle>Posts by Plan</CardTitle>
                <CardDescription>Post creation by subscription plan</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={postActivityData?.postsByPlan || []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="plan" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="post_count" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>User Growth</CardTitle>
              <CardDescription>New user registrations over time</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <AreaChart data={userGrowthData?.userGrowth || []}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="period" />
                  <YAxis />
                  <Tooltip />
                  <Area type="monotone" dataKey="new_users" stroke="#8884d8" fill="#8884d8" fillOpacity={0.6} />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="posts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Post Activity</CardTitle>
              <CardDescription>Posts created over time</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={postActivityData?.postActivity || []}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="period" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="posts_created" stroke="#82ca9d" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="revenue" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Revenue Over Time</CardTitle>
                <CardDescription>Estimated revenue from subscriptions</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={revenueData?.revenueData || []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="period" />
                    <YAxis />
                    <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                    <Area type="monotone" dataKey="estimated_revenue" stroke="#82ca9d" fill="#82ca9d" fillOpacity={0.6} />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Revenue by Plan</CardTitle>
                <CardDescription>Monthly recurring revenue breakdown</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {revenueData?.totalRevenueByPlan?.map((item, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Badge style={{ backgroundColor: getPlanColor(item.plan) }}>
                          {item.plan.toUpperCase()}
                        </Badge>
                        <span className="text-sm text-muted-foreground">
                          ({item.billing_cycle})
                        </span>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">
                          {formatCurrency(item.estimated_monthly_revenue)}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {item.active_subscriptions} subscribers
                        </div>
                      </div>
                    </div>
                  ))}
                  <div className="border-t pt-4">
                    <div className="flex items-center justify-between font-bold">
                      <span>Total MRR</span>
                      <span>
                        {formatCurrency(
                          revenueData?.totalRevenueByPlan?.reduce(
                            (sum, item) => sum + item.estimated_monthly_revenue,
                            0
                          ) || 0
                        )}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
