#!/usr/bin/env node

import Database from 'better-sqlite3';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import readline from 'readline';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Database path
const dbPath = path.join(__dirname, 'data', 'socialspark.db');

// Create readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Helper function to ask questions
function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, resolve);
  });
}

// Available plans
const PLANS = ['free', 'basic', 'pro', 'ultra'];

// Plan limits
const PLAN_LIMITS = {
  free: 3,
  basic: 50,
  pro: 200,
  ultra: 500
};

async function changeUserPlan() {
  try {
    console.log('🔧 SocialSpark User Plan Manager\n');

    // Connect to database
    const db = new Database(dbPath);
    
    // Get email input
    const emailInput = await askQuestion('Enter user email (or partial email): ');
    
    if (!emailInput.trim()) {
      console.log('❌ Email cannot be empty!');
      rl.close();
      return;
    }

    // Search for users
    const searchPattern = `${emailInput.trim()}%`;
    const users = db.prepare(`
      SELECT u.id, u.email, p.full_name, p.plan, p.posts_count, p.posts_limit, u.created_at
      FROM users u
      LEFT JOIN profiles p ON u.id = p.id
      WHERE u.email LIKE ?
      ORDER BY u.email
    `).all(searchPattern);

    if (users.length === 0) {
      console.log(`❌ No users found with email starting with "${emailInput}"`);
      rl.close();
      return;
    }

    // Display found users
    console.log(`\n📋 Found ${users.length} user(s):`);
    console.log('─'.repeat(80));
    
    users.forEach((user, index) => {
      console.log(`${index + 1}. Email: ${user.email}`);
      console.log(`   Name: ${user.full_name || 'N/A'}`);
      console.log(`   Current Plan: ${user.plan || 'N/A'}`);
      console.log(`   Posts: ${user.posts_count || 0}/${user.posts_limit || 0}`);
      console.log(`   Created: ${user.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}`);
      console.log('');
    });

    // Select user if multiple found
    let selectedUser;
    if (users.length === 1) {
      selectedUser = users[0];
      console.log(`✅ Selected user: ${selectedUser.email}`);
    } else {
      const userChoice = await askQuestion(`Select user (1-${users.length}): `);
      const userIndex = parseInt(userChoice) - 1;
      
      if (userIndex < 0 || userIndex >= users.length) {
        console.log('❌ Invalid selection!');
        rl.close();
        return;
      }
      
      selectedUser = users[userIndex];
      console.log(`✅ Selected user: ${selectedUser.email}`);
    }

    // Display available plans
    console.log('\n📋 Available plans:');
    console.log('─'.repeat(30));
    PLANS.forEach((plan, index) => {
      const limit = PLAN_LIMITS[plan];
      const current = plan === selectedUser.plan ? ' (CURRENT)' : '';
      console.log(`${index + 1}. ${plan.toUpperCase()} - ${limit} posts/month${current}`);
    });

    // Get new plan
    const planChoice = await askQuestion('\nSelect new plan (1-4): ');
    const planIndex = parseInt(planChoice) - 1;
    
    if (planIndex < 0 || planIndex >= PLANS.length) {
      console.log('❌ Invalid plan selection!');
      rl.close();
      return;
    }

    const newPlan = PLANS[planIndex];
    const newLimit = PLAN_LIMITS[newPlan];

    // Confirm change
    console.log(`\n🔄 Plan Change Summary:`);
    console.log(`   User: ${selectedUser.email}`);
    console.log(`   Current Plan: ${selectedUser.plan || 'N/A'} (${selectedUser.posts_limit || 0} posts/month)`);
    console.log(`   New Plan: ${newPlan.toUpperCase()} (${newLimit} posts/month)`);
    
    const confirm = await askQuestion('\nConfirm change? (y/N): ');
    
    if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
      console.log('❌ Operation cancelled.');
      rl.close();
      return;
    }

    // Update the plan
    const now = new Date().toISOString();
    
    // Add [PLAN UPDATED] flag to the full_name to trigger refresh in frontend
    const updatedFullName = selectedUser.full_name ? 
      `${selectedUser.full_name.replace(/\s*\[PLAN UPDATED\]\s*/, '')} [PLAN UPDATED]` : 
      'User [PLAN UPDATED]';

    const updateResult = db.prepare(`
      UPDATE profiles 
      SET plan = ?, posts_limit = ?, full_name = ?, updated_at = ?
      WHERE id = ?
    `).run(newPlan, newLimit, updatedFullName, now, selectedUser.id);

    if (updateResult.changes > 0) {
      console.log('✅ Plan updated successfully!');
      console.log(`📧 User ${selectedUser.email} is now on the ${newPlan.toUpperCase()} plan`);
      console.log(`📊 New post limit: ${newLimit} posts/month`);
      console.log('🔄 User will see the update on next app refresh');
    } else {
      console.log('❌ Failed to update plan. User might not have a profile.');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    rl.close();
  }
}

// Run the script
changeUserPlan();