#!/usr/bin/env node

import { db } from './server/sqlite-db.js';

console.log('Adding admin field to profiles table...');

try {
  // Check if admin column exists
  const tableInfo = db.prepare("PRAGMA table_info(profiles)").all();
  const columnNames = tableInfo.map(col => col.name);
  
  if (!columnNames.includes('is_admin')) {
    db.prepare('ALTER TABLE profiles ADD COLUMN is_admin INTEGER DEFAULT 0').run();
    console.log('Added is_admin column to profiles table');
  } else {
    console.log('is_admin column already exists');
  }
  
  console.log('Database update completed successfully');
} catch (error) {
  console.error('Error updating database:', error);
  process.exit(1);
}