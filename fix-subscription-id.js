import Database from 'better-sqlite3';

const db = new Database('./data/socialspark.db');

// Update the user's subscription ID based on the logs
const userId = 'b46f5a98-1422-492e-8723-af8aca9771f2';
const subscriptionId = 'I-8CS0YG75J8FW';

try {
  const stmt = db.prepare(`
    UPDATE profiles 
    SET paypal_subscription_id = ?, 
        billing_cycle = 'monthly',
        next_billing_date = ?, 
        updated_at = ?
    WHERE id = ?
  `);
  
  const nextBilling = new Date();
  nextBilling.setMonth(nextBilling.getMonth() + 1);
  
  const result = stmt.run(
    subscriptionId, 
    nextBilling.toISOString(),
    new Date().toISOString(),
    userId
  );
  
  console.log('Update result:', result);
  
  // Verify the update
  const profile = db.prepare('SELECT * FROM profiles WHERE id = ?').get(userId);
  console.log('Updated profile:', profile);
  
} catch (error) {
  console.error('Error updating subscription ID:', error);
} finally {
  db.close();
}