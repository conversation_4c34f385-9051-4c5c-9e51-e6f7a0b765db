
import { <PERSON> } from "react-router-dom"
import { Instagram, <PERSON>ed<PERSON> } from "lucide-react"
import XIcon from "@/components/icons/XIcon"

export default function Footer() {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="bg-background border-t py-12 px-4">
      <div className="max-w-7xl mx-auto grid grid-cols-1 md:grid-cols-4 gap-8 md:gap-12">
        <div className="col-span-1 md:col-span-2">
          <Link to="/" className="flex items-center space-x-2">
            <img src="/logo.png" alt="SocialSparkGen Logo" className="h-8 w-8" />
            <span className="text-xl font-bold">SocialSparkGen</span>
          </Link>
          <p className="mt-4 text-muted-foreground max-w-md">
            Generate engaging captions and visuals in seconds—no creative block, no fuss.
            Spark your social presence with AI-powered posts.
          </p>
          <div className="flex space-x-4 mt-6">
            <a href="#" className="text-muted-foreground hover:text-primary transition-colors">
              <XIcon className="h-5 w-5" />
              <span className="sr-only">X (Twitter)</span>
            </a>
            <a href="#" className="text-muted-foreground hover:text-primary transition-colors">
              <Instagram className="h-5 w-5" />
              <span className="sr-only">Instagram</span>
            </a>
            <a href="#" className="text-muted-foreground hover:text-primary transition-colors">
              <Linkedin className="h-5 w-5" />
              <span className="sr-only">LinkedIn</span>
            </a>
          </div>
        </div>

        <div>
          <h3 className="font-medium text-foreground mb-3">Pages</h3>
          <ul className="space-y-2">
            <li>
              <Link to="/" className="text-muted-foreground hover:text-primary transition-colors">
                Home
              </Link>
            </li>
            <li>
              <Link to="/features" className="text-muted-foreground hover:text-primary transition-colors">
                Features
              </Link>
            </li>
            <li>
              <Link to="/examples" className="text-muted-foreground hover:text-primary transition-colors">
                Examples
              </Link>
            </li>
            <li>
              <Link to="/pricing" className="text-muted-foreground hover:text-primary transition-colors">
                Pricing
              </Link>
            </li>
          </ul>
        </div>

        <div>
          <h3 className="font-medium text-foreground mb-3">Contact</h3>
          <ul className="space-y-2">
            <li>
              <Link to="/contact" className="text-muted-foreground hover:text-primary transition-colors">
                Contact Us
              </Link>
            </li>
            <li>
              <a href="mailto:<EMAIL>" className="text-muted-foreground hover:text-primary transition-colors">
                <EMAIL>
              </a>
            </li>
            <li>
              <a href="#" className="text-muted-foreground hover:text-primary transition-colors">
                Privacy Policy
              </a>
            </li>
            <li>
              <a href="#" className="text-muted-foreground hover:text-primary transition-colors">
                Terms of Service
              </a>
            </li>
          </ul>
        </div>
      </div>

      <div className="max-w-7xl mx-auto mt-12 pt-6 border-t">
        <p className="text-muted-foreground text-center text-sm">
          © {currentYear} SocialSparkGen. All rights reserved.
        </p>
      </div>
    </footer>
  )
}
