# Google AI API Key for content generation
GOOGLE_API_KEY=your_google_ai_api_key_here

# PayPal Configuration
# Get these from https://developer.paypal.com/
PAYPAL_CLIENT_ID=your_paypal_client_id_here
PAYPAL_CLIENT_SECRET=your_paypal_client_secret_here

# PayPal Environment: 'sandbox' for testing, 'live' for production
PAYPAL_ENVIRONMENT=sandbox

# PayPal Subscription Plans (create these in PayPal Developer Console)
# Format: PLAN_NAME_CYCLE_PRICE_INTERVAL
# Interval: 1DAY, 1WEEK, 1MONTH, 1YEAR

# Basic Plan Subscriptions
PAYPAL_BASIC_MONTHLY_PRICE=5
PAYPAL_BASIC_MONTHLY_INTERVAL=1MONTH
PAYPAL_BASIC_YEARLY_PRICE=48
PAYPAL_BASIC_YEARLY_INTERVAL=1YEAR

# Pro Plan Subscriptions
PAYPAL_PRO_MONTHLY_PRICE=15
PAYPAL_PRO_MONTHLY_INTERVAL=1MONTH
PAYPAL_PRO_YEARLY_PRICE=144
PAYPAL_PRO_YEARLY_INTERVAL=1YEAR

# Ultra Plan Subscriptions
PAYPAL_ULTRA_MONTHLY_PRICE=30
PAYPAL_ULTRA_MONTHLY_INTERVAL=1MONTH
PAYPAL_ULTRA_YEARLY_PRICE=300
PAYPAL_ULTRA_YEARLY_INTERVAL=1YEAR

# Global Rate Limiting Configuration
# Maximum posts per minute across all users before blocking FREE users
GLOBAL_RATE_LIMIT_FREE=30
# Maximum posts per minute across all users before blocking BASIC users
GLOBAL_RATE_LIMIT_BASIC=65

# Server Configuration
PORT=3001

# Frontend PayPal Client ID (can be the same as backend, but exposed to frontend)
VITE_PAYPAL_CLIENT_ID=your_paypal_client_id_here

# Frontend API Base URL - change this for production deployment
# For local development: http://localhost:5001
# For production: https://your-domain.com (or leave empty to use relative URLs)
VITE_API_BASE_URL=