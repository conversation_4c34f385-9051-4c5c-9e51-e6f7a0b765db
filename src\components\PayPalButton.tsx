import React from 'react';
import { PayPalButtons, usePayPalScriptReducer } from '@paypal/react-paypal-js';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { usePayPal } from '@/hooks/usePayPal';

interface PayPalButtonProps {
  planName: string;
  amount: string;
  billingCycle: 'monthly' | 'yearly';
  onSuccess?: (details: any) => void;
  onError?: (error: any) => void;
  className?: string;
}

export default function PayPalButton({ 
  planName, 
  amount, 
  billingCycle, 
  onSuccess, 
  onError,
  className 
}: PayPalButtonProps) {
  const { config, loading: configLoading, error: configError } = usePayPal();
  const { toast } = useToast();

  // If PayPal config is not available, show fallback button
  if (configLoading || configError || !config) {
    return (
      <Button className={className} disabled>
        {configLoading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Loading PayPal...
          </>
        ) : (
          'PayPal Unavailable'
        )}
      </Button>
    );
  }

  return <PayPalButtonInner {...{ planName, amount, billingCycle, onSuccess, onError, className }} />;
}

function PayPalButtonInner({ 
  planName, 
  amount, 
  billingCycle, 
  onSuccess, 
  onError,
  className 
}: PayPalButtonProps) {
  const [{ isLoading, isResolved }] = usePayPalScriptReducer();
  const { toast } = useToast();

  const createOrder = (data: any, actions: any) => {
    return actions.order.create({
      purchase_units: [
        {
          amount: {
            value: amount,
            currency_code: 'USD'
          },
          description: `${planName} Plan - ${billingCycle} billing`
        }
      ],
      intent: 'CAPTURE'
    });
  };

  const onApprove = async (data: any, actions: any) => {
    try {
      console.log('PayPal onApprove called with data:', data);
      
      // Capture the payment on PayPal's side
      const details = await actions.order.capture();
      console.log('PayPal capture successful:', details);
      
      // Get fresh token from localStorage
      let token = localStorage.getItem('auth_token');
      console.log('Token exists:', !!token);
      
      if (!token) {
        throw new Error('Please log in again to complete your purchase');
      }
      
      console.log('Sending capture request to backend...');
      
      const response = await fetch('/api/payments/paypal/capture', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          orderID: data.orderID,
          planName,
          billingCycle,
          paymentDetails: details
        })
      });

      console.log('Backend response status:', response.status);

      if (response.status === 401 || response.status === 403) {
        // Token is invalid - clear it and ask user to log in again
        localStorage.removeItem('auth_token');
        throw new Error('Your session has expired. Please log in again to complete your purchase. Your payment was successful and will be processed manually.');
      }

      if (response.ok) {
        const result = await response.json();
        console.log('Payment verification successful:', result);
        toast({
          title: "Payment successful!",
          description: `Welcome to ${planName} plan! Your account has been upgraded.`,
        });
        onSuccess?.(result);
      } else {
        const error = await response.json();
        console.error('Backend verification failed:', error);
        throw new Error(error.error || error.message || 'Payment verification failed');
      }
    } catch (error) {
      console.error('Payment error:', error);
      toast({
        title: "Payment Issue",
        description: error instanceof Error ? error.message : "Something went wrong with your payment.",
        variant: "destructive",
      });
      onError?.(error);
    }
  };

  const onErrorHandler = (error: any) => {
    console.error('PayPal error:', error);
    toast({
      title: "Payment error",
      description: "There was an issue processing your payment. Please try again.",
      variant: "destructive",
    });
    onError?.(error);
  };

  if (isLoading) {
    return (
      <Button className={className} disabled>
        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        Loading PayPal...
      </Button>
    );
  }

  if (!isResolved) {
    return (
      <Button className={className} disabled>
        PayPal Unavailable
      </Button>
    );
  }

  return (
    <div className="w-full">
      <PayPalButtons
        style={{
          layout: 'vertical',
          color: 'blue',
          shape: 'rect',
          label: 'pay'
        }}
        createOrder={createOrder}
        onApprove={onApprove}
        onError={onErrorHandler}
        onCancel={() => {
          toast({
            title: "Payment cancelled",
            description: "You can complete your purchase anytime.",
          });
        }}
      />
    </div>
  );
}