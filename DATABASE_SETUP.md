# Database Setup Guide

SocialSparkAI supports both SQLite and MySQL databases. You can choose which one to use by setting the `DATABASE_TYPE` environment variable.

## Quick Start

### Using SQLite (Default)
SQLite is the default database and requires no additional setup. Just run the application and it will automatically create the database file.

```bash
# In your .env file
DATABASE_TYPE=sqlite
```

### Using MySQL
To use MySQL, you need to set up a MySQL server and configure the connection details.

```bash
# In your .env file
DATABASE_TYPE=mysql
DB_HOST=localhost
DB_PORT=3306
DB_NAME=socialspark
DB_USER=root
DB_PASSWORD=your_mysql_password
```

## Detailed Setup Instructions

### SQLite Setup (Recommended for Development)

1. **No additional setup required** - SQLite is included with Node.js
2. The database file will be created automatically at `data/socialspark.db`
3. Set environment variables in `.env`:
   ```
   DATABASE_TYPE=sqlite
   ```

**Pros:**
- Zero configuration
- Perfect for development and testing
- Lightweight and fast
- No separate server required

**Cons:**
- Not suitable for high-concurrency production environments
- Limited to single-server deployments

### MySQL Setup (Recommended for Production)

#### 1. Install MySQL Server

**On Windows:**
- Download MySQL from https://dev.mysql.com/downloads/mysql/
- Run the installer and follow the setup wizard
- Remember the root password you set during installation

**On macOS:**
```bash
# Using Homebrew
brew install mysql
brew services start mysql
```

**On Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install mysql-server
sudo mysql_secure_installation
```

#### 2. Create Database and User

Connect to MySQL as root:
```bash
mysql -u root -p
```

Run the setup script:
```sql
-- Create database
CREATE DATABASE socialspark CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create a dedicated user (recommended)
CREATE USER 'socialspark_user'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON socialspark.* TO 'socialspark_user'@'localhost';
FLUSH PRIVILEGES;

-- Exit MySQL
EXIT;
```

Or use the provided setup script:
```bash
mysql -u root -p < scripts/setup-mysql.sql
```

#### 3. Configure Environment Variables

Update your `.env` file:
```
DATABASE_TYPE=mysql
DB_HOST=localhost
DB_PORT=3306
DB_NAME=socialspark
DB_USER=socialspark_user
DB_PASSWORD=your_secure_password
```

**Pros:**
- Excellent for production environments
- Supports high concurrency
- Advanced features like replication, clustering
- Better performance for large datasets

**Cons:**
- Requires separate MySQL server
- More complex setup and maintenance
- Additional resource requirements

## Environment Variables Reference

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `DATABASE_TYPE` | Database type (`sqlite` or `mysql`) | `sqlite` | No |
| `DB_HOST` | MySQL host address | `localhost` | MySQL only |
| `DB_PORT` | MySQL port number | `3306` | MySQL only |
| `DB_NAME` | MySQL database name | `socialspark` | MySQL only |
| `DB_USER` | MySQL username | `root` | MySQL only |
| `DB_PASSWORD` | MySQL password | - | MySQL only |

## Migration Between Databases

### From SQLite to MySQL

1. Set up MySQL database using the instructions above
2. Export data from SQLite (if you have existing data)
3. Update `.env` to use MySQL
4. Restart the application
5. Import data to MySQL (if applicable)

### From MySQL to SQLite

1. Export data from MySQL (if you have existing data)
2. Update `.env` to use SQLite
3. Restart the application
4. Import data to SQLite (if applicable)

## Troubleshooting

### Common SQLite Issues

**Issue:** Permission denied when creating database file
**Solution:** Ensure the `data` directory is writable by the application

**Issue:** Database is locked
**Solution:** Make sure no other processes are accessing the database file

### Common MySQL Issues

**Issue:** Connection refused
**Solution:** 
- Ensure MySQL server is running
- Check host and port settings
- Verify firewall settings

**Issue:** Access denied for user
**Solution:**
- Verify username and password
- Check user privileges
- Ensure user can connect from the specified host

**Issue:** Unknown database
**Solution:**
- Create the database using the setup script
- Verify database name in environment variables

### Testing Database Connection

The application includes a health check endpoint that shows database status:

```bash
curl http://localhost:3001/api/health
```

Response example:
```json
{
  "status": "ok",
  "database": "connected",
  "type": "mysql"
}
```

## Performance Considerations

### SQLite
- Use WAL mode for better concurrency (automatically enabled)
- Regular VACUUM operations for maintenance
- Consider connection pooling for high-load scenarios

### MySQL
- Configure appropriate connection pool size
- Use indexes on frequently queried columns
- Regular maintenance (OPTIMIZE TABLE, ANALYZE TABLE)
- Monitor slow query log

## Security Best Practices

### SQLite
- Ensure database file has appropriate permissions
- Regular backups of the database file
- Consider encryption for sensitive data

### MySQL
- Use dedicated database user with minimal privileges
- Enable SSL/TLS for connections
- Regular security updates
- Strong passwords and authentication
- Network security (firewall, VPN)

## Backup and Recovery

### SQLite
```bash
# Backup
cp data/socialspark.db backup/socialspark_$(date +%Y%m%d).db

# Restore
cp backup/socialspark_20231201.db data/socialspark.db
```

### MySQL
```bash
# Backup
mysqldump -u socialspark_user -p socialspark > backup/socialspark_$(date +%Y%m%d).sql

# Restore
mysql -u socialspark_user -p socialspark < backup/socialspark_20231201.sql
```
