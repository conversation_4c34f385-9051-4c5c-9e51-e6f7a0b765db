#!/usr/bin/env node

import { db } from './server/sqlite-db.js';

try {
  const users = db.prepare(`
    SELECT u.email, p.full_name, p.is_admin 
    FROM users u 
    JOIN profiles p ON u.id = p.id
  `).all();

  console.log('Existing users:');
  if (users.length === 0) {
    console.log('No users found. Please register first.');
  } else {
    users.forEach(user => {
      console.log(`- ${user.email} (${user.full_name}) - Admin: ${user.is_admin ? 'Yes' : 'No'}`);
    });
  }
} catch (error) {
  console.error('Error listing users:', error);
}