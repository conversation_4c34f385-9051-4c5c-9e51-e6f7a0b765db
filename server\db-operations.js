import { DATABASE_TYPE, db, pool, testConnection } from './database.js';

// Unified database operations that work with both SQLite and MySQL

// Execute a query and return a single row
export const getOne = async (sql, params = []) => {
  try {
    if (DATABASE_TYPE === 'mysql') {
      const connection = await pool.getConnection();
      try {
        const [rows] = await connection.query(sql, params);
        connection.release();
        return rows[0] || null;
      } catch (error) {
        connection.release();
        throw error;
      }
    } else {
      // SQLite
      const stmt = db.prepare(sql);
      return stmt.get(params);
    }
  } catch (error) {
    console.error('Error executing getOne query:', error);
    throw error;
  }
};

// Execute a query and return multiple rows
export const getAll = async (sql, params = []) => {
  try {
    if (DATABASE_TYPE === 'mysql') {
      const connection = await pool.getConnection();
      try {
        const [rows] = await connection.query(sql, params);
        connection.release();
        return rows;
      } catch (error) {
        connection.release();
        throw error;
      }
    } else {
      // SQLite
      const stmt = db.prepare(sql);
      return stmt.all(params);
    }
  } catch (error) {
    console.error('Error executing getAll query:', error);
    throw error;
  }
};

// Execute a query that modifies data (INSERT, UPDATE, DELETE)
export const run = async (sql, params = []) => {
  try {
    if (DATABASE_TYPE === 'mysql') {
      const connection = await pool.getConnection();
      try {
        const [result] = await connection.query(sql, params);
        connection.release();
        return {
          changes: result.affectedRows || 0,
          lastInsertRowid: result.insertId || null
        };
      } catch (error) {
        connection.release();
        throw error;
      }
    } else {
      // SQLite
      const stmt = db.prepare(sql);
      return stmt.run(params);
    }
  } catch (error) {
    console.error('Error executing run query:', error);
    throw error;
  }
};

// Execute multiple queries in a transaction
export const transaction = async (queries) => {
  if (DATABASE_TYPE === 'mysql') {
    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();
      
      const results = [];
      for (const { sql, params } of queries) {
        const [result] = await connection.query(sql, params);
        results.push(result);
      }
      
      await connection.commit();
      connection.release();
      return results;
    } catch (error) {
      await connection.rollback();
      connection.release();
      throw error;
    }
  } else {
    // SQLite
    return db.transaction(() => {
      const results = [];
      for (const { sql, params } of queries) {
        const stmt = db.prepare(sql);
        results.push(stmt.run(params));
      }
      return results;
    })();
  }
};

// Get database type
export const getDatabaseType = () => DATABASE_TYPE;

// Close database connection
export const closeConnection = async () => {
  try {
    if (DATABASE_TYPE === 'mysql' && pool) {
      await pool.end();
      console.log('MySQL connection pool closed');
    } else if (DATABASE_TYPE === 'sqlite' && db) {
      db.close();
      console.log('SQLite connection closed');
    }
  } catch (error) {
    console.error('Error closing database connection:', error);
  }
};

// Helper function to convert boolean values for database compatibility
export const convertBoolean = (value) => {
  if (DATABASE_TYPE === 'mysql') {
    return value; // MySQL supports native boolean
  } else {
    return value ? 1 : 0; // SQLite uses integers for boolean
  }
};

// Helper function to convert database boolean values back to JavaScript boolean
export const parseBoolean = (value) => {
  if (DATABASE_TYPE === 'mysql') {
    return Boolean(value);
  } else {
    return value === 1;
  }
};

// Helper function to format SQL for different database types
export const formatSQL = (sql) => {
  if (DATABASE_TYPE === 'mysql') {
    // Convert SQLite-specific syntax to MySQL
    return sql
      .replace(/INTEGER/g, 'INT')
      .replace(/TEXT/g, 'VARCHAR(255)')
      .replace(/CURRENT_TIMESTAMP/g, 'NOW()')
      .replace(/\?/g, '?'); // MySQL uses ? for parameters
  } else {
    // Keep SQLite syntax
    return sql;
  }
};

// Helper function to get the appropriate LIMIT/OFFSET syntax
export const getLimitOffset = (limit, offset) => {
  if (DATABASE_TYPE === 'mysql') {
    return `LIMIT ${limit} OFFSET ${offset}`;
  } else {
    return `LIMIT ${limit} OFFSET ${offset}`;
  }
};

// Helper function to get current timestamp in the appropriate format
export const getCurrentTimestamp = () => {
  if (DATABASE_TYPE === 'mysql') {
    return new Date().toISOString().slice(0, 19).replace('T', ' ');
  } else {
    return new Date().toISOString();
  }
};

// Analytics helper functions
export const trackUserSession = async (userId, sessionToken, ipAddress, userAgent) => {
  const { v4: uuidv4 } = await import('uuid');
  const sessionId = uuidv4();
  
  if (DATABASE_TYPE === 'mysql') {
    return await run(`
      INSERT INTO user_sessions (id, user_id, session_token, last_activity, ip_address, user_agent)
      VALUES (?, ?, ?, NOW(), ?, ?)
      ON DUPLICATE KEY UPDATE
      last_activity = NOW(), ip_address = VALUES(ip_address), user_agent = VALUES(user_agent)
    `, [sessionId, userId, sessionToken, ipAddress, userAgent]);
  } else {
    return await run(`
      INSERT OR REPLACE INTO user_sessions (id, user_id, session_token, last_activity, ip_address, user_agent)
      VALUES (?, ?, ?, CURRENT_TIMESTAMP, ?, ?)
    `, [sessionId, userId, sessionToken, ipAddress, userAgent]);
  }
};

export const updateUserActivity = async (sessionToken) => {
  if (DATABASE_TYPE === 'mysql') {
    return await run(`
      UPDATE user_sessions 
      SET last_activity = NOW() 
      WHERE session_token = ?
    `, [sessionToken]);
  } else {
    return await run(`
      UPDATE user_sessions 
      SET last_activity = CURRENT_TIMESTAMP 
      WHERE session_token = ?
    `, [sessionToken]);
  }
};

export const removeUserSession = async (sessionToken) => {
  return await run(`
    DELETE FROM user_sessions 
    WHERE session_token = ?
  `, [sessionToken]);
};

export const getOnlineUsersCount = async () => {
  const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
  
  if (DATABASE_TYPE === 'mysql') {
    const result = await getOne(`
      SELECT COUNT(DISTINCT user_id) as count 
      FROM user_sessions 
      WHERE last_activity > ?
    `, [fiveMinutesAgo.toISOString().slice(0, 19).replace('T', ' ')]);
    return result?.count || 0;
  } else {
    const result = await getOne(`
      SELECT COUNT(DISTINCT user_id) as count 
      FROM user_sessions 
      WHERE last_activity > ?
    `, [fiveMinutesAgo.toISOString()]);
    return result?.count || 0;
  }
};

export const trackAnalyticsEvent = async (eventType, userId = null, sessionId = null, ipAddress = null, userAgent = null, eventData = null) => {
  const { v4: uuidv4 } = await import('uuid');
  const eventId = uuidv4();
  
  let eventDataString = null;
  if (eventData) {
    eventDataString = DATABASE_TYPE === 'mysql' ? JSON.stringify(eventData) : JSON.stringify(eventData);
  }
  
  return await run(`
    INSERT INTO analytics_events (id, event_type, user_id, session_id, ip_address, user_agent, event_data)
    VALUES (?, ?, ?, ?, ?, ?, ?)
  `, [eventId, eventType, userId, sessionId, ipAddress, userAgent, eventDataString]);
};

// Export the database instance for direct access when needed
export { db, pool, testConnection };
