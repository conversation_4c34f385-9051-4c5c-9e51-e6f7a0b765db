# SocialSparkGen Development Guide

## Project Overview
SocialSparkGen is a web application that generates AI-powered social media content using Google's Gemini API. Users can create posts with captions, hashtags, and images across multiple platforms.

## Commands

### Development
- `npm start` - Start frontend development server
- `cd server && node index.js` - Start backend server
- `npm run build` - Build for production

### Testing
- `node test-security.js` - Test security implementations
- `npm test` - Run frontend tests (if available)

## Security Features Implemented

### 1. Input Validation with Zod
- **Location**: `server/validation.js`
- **Coverage**: All user inputs (registration, login, post generation, profile updates)
- **Schemas**: registerSchema, loginSchema, generatePostSchema, etc.
- **Usage**: Applied via middleware to all API endpoints
- **Fixed**: Schema now matches frontend data format (Platform: "Instagram", Tone: "Professional")

### 2. Security Headers with Helmet
- **Implemented**: Content Security Policy, XSS Protection, Frame Options, HSTS
- **Location**: `server/index.js` middleware section
- **Headers Added**:
  - X-Content-Type-Options: nosniff
  - X-Frame-Options: SAMEORIGIN
  - Strict-Transport-Security
  - Content-Security-Policy

### 3. IP-Based Account Limiting
- **Rule**: Maximum 1 account per IP address
- **Implementation**: `checkIPLimit` middleware in `server/index.js`
- **Applied to**: Registration endpoint only
- **Database**: Tracks IP addresses in `user_sessions` table

### 4. Input Sanitization
- **Function**: `sanitizeObject` in `server/validation.js`
- **Applied**: Automatically to all request bodies
- **Protection**: Basic XSS prevention, removes HTML tags and event handlers

## Database Schema

### Core Tables
- `users` - Authentication (email, password_hash)
- `profiles` - User profiles (full_name, plan, posts_count)
- `posts` - Generated content
- `user_sessions` - Session tracking with IP addresses
- `analytics_events` - Usage analytics

### Security Considerations
- JWT tokens with 7-day expiry
- PBKDF2 password hashing with salt (1000 iterations)
- Foreign key constraints for data integrity
- IP address tracking for account limits

## API Endpoints

### Authentication
- `POST /api/auth/register` - User registration (validated, IP-limited)
- `POST /api/auth/login` - User login (validated)

### Content Generation
- `POST /api/generate-post` - Generate social media post (validated)
- `POST /api/generate-image` - Generate images (validated)

### User Management
- `GET /api/profile` - Get user profile
- `PATCH /api/profile` - Update profile (validated)

## Environment Variables
```
GOOGLE_API_KEY=your_google_api_key
JWT_SECRET=your_jwt_secret_key_here
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PORT=5001
NODE_ENV=development
```

## Codebase Structure
```
/
├── src/                 # React frontend
├── server/             # Express backend
│   ├── index.js        # Main server file
│   ├── validation.js   # Zod schemas & sanitization
│   ├── sqlite-auth.js  # Authentication logic
│   ├── sqlite-db.js    # Database operations
│   └── admin-middleware.js # Admin access control
├── package.json        # Dependencies
└── .env               # Environment variables
```

## Security Best Practices Applied

1. **Authentication**: JWT tokens with proper verification
2. **Authorization**: Admin middleware for protected routes
3. **Input Validation**: Comprehensive Zod schemas
4. **Rate Limiting**: Per-user and per-IP restrictions
5. **Password Security**: PBKDF2 hashing with salt
6. **Session Management**: IP-tracked sessions
7. **Security Headers**: Helmet middleware protection
8. **Input Sanitization**: XSS prevention on all inputs

## Known Security Considerations

### For 200 Monthly Users (Current Scale)
- Current security level: **Good** (7/10)
- IP limiting may be too restrictive for shared networks
- Consider implementing email verification for production
- Monitor for API key usage limits

### Potential Improvements
- Add rate limiting middleware for API endpoints
- Implement email verification for registration
- Add password complexity requirements
- Consider 2FA for admin accounts
- Add audit logging for admin actions

## Testing
- Security tests available in `test-security.js`
- Tests cover validation, sanitization, headers, and IP limiting
- Run tests to verify security implementations are working

## Deployment Notes
- Ensure strong JWT_SECRET in production
- Use HTTPS in production environment
- Consider relaxing IP limits for shared environments
- Monitor authentication logs for suspicious activity