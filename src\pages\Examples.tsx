
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Instagram, Linkedin, Facebook } from "lucide-react"
import XIcon from "@/components/icons/XIcon"
import { useTheme } from "@/components/ThemeProvider"
import { Link } from "react-router-dom"

// Define types for our post objects
interface ExamplePost {
  caption: string;
  tags: string[];
  imageIndex: number; // 1 or 2 for different example variations
}

interface ExamplePosts {
  x: ExamplePost[];
  instagram: ExamplePost[];
  linkedin: ExamplePost[];
  facebook: ExamplePost[];
}

export default function Examples() {
  const { theme } = useTheme();

  // Helper function to generate image path based on platform, theme, and index
  const getExampleImagePath = (platform: string, themeMode: string, index: number) => {
    return `/public/${platform}_${themeMode}_${index}.png`;
  };

  // Helper function to get the correct image based on theme
  const getThemeImage = (post: ExamplePost, platform: string) => {
    const themeMode = theme === "dark" ? "dark" : "light";
    return getExampleImagePath(platform, themeMode, post.imageIndex);
  };

  const examplePosts: ExamplePosts = {
    x: [
      {
        caption: "Just launched our new productivity app! 🚀 After 6 months of development, we're excited to share it with the world. Try it free for 30 days and let us know what you think! #ProductivityApp #NewLaunch #TechStartup",
        tags: [],
        imageIndex: 1
      },
      {
        caption: "The 5-minute morning ritual that's transformed my workflow. No fancy tools required, just intention and focus. Who else starts their day with a moment of planning? #MorningRitual #Productivity #WorkLifeBalance",
        tags: [],
        imageIndex: 2
      },
    ],
    instagram: [
      {
        caption: "✨ Embracing the journey, not just the destination. This week I'm focusing on enjoying the process of building rather than fixating on results. What's your mindset shift for the week ahead?\n\n#MindsetMatters #EntrepreneurLife #GrowthJourney #BusinessBuilding",
        tags: [],
        imageIndex: 1
      },
      {
        caption: "Sunday coffee and strategy sessions ☕️\nMapping out content for the week while enjoying this perfect brew from @localcoffeeshop. What's your Sunday ritual that sets up your week for success?\n\n#SundayRitual #ContentCreation #CoffeeAndContent #WeeklyPlanning",
        tags: [],
        imageIndex: 2
      },
    ],
    linkedin: [
      {
        caption: "Excited to announce that our team has grown by 25% this quarter! 🎉\n\nAs we scale, we're focused on maintaining our collaborative culture while embracing new perspectives.\n\nWhat strategies have you found effective for preserving company culture during periods of rapid growth?",
        tags: [],
        imageIndex: 1
      },
      {
        caption: "Just published: \"5 Data-Driven Marketing Strategies That Increased Our Conversion Rate by 43%\"\n\nIn this article, I break down the methodical approach our team used to identify customer pain points and optimize the buyer journey.\n\nThe most surprising finding? Sometimes less is more when it comes to touchpoints.",
        tags: [],
        imageIndex: 2
      },
    ],
    facebook: [
      {
        caption: "🎁 GIVEAWAY ALERT! 🎁\nWe're celebrating our 5-year anniversary by giving away 5 deluxe gift sets valued at $200 each!\n\nTo enter:\n✅ Like this post\n✅ Follow our page\n✅ Tag a friend who would love our products\n✅ Share this post to your story\n\nWinners announced next Monday. Good luck everyone!",
        tags: [],
        imageIndex: 1
      },
      {
        caption: "We're thrilled to announce our community clean-up event happening next Saturday at River Park! 🌿 Join us from 10am-2pm as we work together to beautify our shared spaces. We'll provide all supplies and refreshments.\n\nTag a friend you'd like to volunteer with! #CommunityMatters #CleanUpEvent",
        tags: [],
        imageIndex: 2
      },
    ],
  }

  const platforms = [
    { value: "x", label: "X (Twitter)", icon: XIcon, color: "text-gray-800" },
    { value: "instagram", label: "Instagram", icon: Instagram, color: "text-pink-500" },
    { value: "linkedin", label: "LinkedIn", icon: Linkedin, color: "text-blue-700" },
    { value: "facebook", label: "Facebook", icon: Facebook, color: "text-blue-600" },
  ]

  return (
    <div className="flex flex-col min-h-screen">
      <section className="section-padding">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">See It in Action</h1>
            <p className="text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto">
              Browse through examples of AI-generated social media posts tailored for
              different platforms and purposes. Each post is optimized for maximum engagement.
            </p>
          </div>

          <Tabs defaultValue="x" className="w-full">
            <div className="flex justify-center mb-8">
              <TabsList>
                {platforms.map((platform) => (
                  <TabsTrigger key={platform.value} value={platform.value} className="flex items-center gap-2">
                    <platform.icon className={`h-4 w-4 ${platform.color}`} /> {platform.label}
                  </TabsTrigger>
                ))}
              </TabsList>
            </div>

            {platforms.map((platform) => (
              <TabsContent key={platform.value} value={platform.value} className="space-y-6">
                <div className="grid sm:grid-cols-2 gap-5">
                  {examplePosts[platform.value as keyof typeof examplePosts].map((post, index) => (
                    <div key={index} className="bg-card rounded-lg border shadow-sm overflow-hidden card-hover">
                      <div className="p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-2">
                            <platform.icon className={`h-5 w-5 ${platform.color}`} />
                            <span className="font-medium">{platform.label} Post</span>
                          </div>
                        </div>
                        <div className="aspect-auto rounded-md overflow-hidden">
                          <img
                            src={getThemeImage(post, platform.value)}
                            alt={`${platform.label} post example`}
                            className="w-full h-auto object-cover"
                          />
                        </div>
                      </div>
                      <div className="bg-muted/30 px-4 py-3 text-xs text-muted-foreground flex justify-center items-center">
                        <span>AI Generated</span>
                      </div>
                    </div>
                  ))}
                </div>


              </TabsContent>
            ))}
          </Tabs>

          <div className="mt-16 bg-card border rounded-lg p-8 text-center">
            <h3 className="text-2xl font-bold mb-3">Create Your Own Posts Now</h3>
            <p className="text-muted-foreground mb-6 max-w-xl mx-auto">
              Ready to see what SocialSparkGen can create for your unique brand and content needs?
              Generate your own AI-powered social media posts in seconds.
            </p>
            <Button size="lg" className="gradient-button" asChild>
              <Link to="/create">Generate Your Own</Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}
