# SocialSparkGen Backend Server

This is the backend server for SocialSparkGen, providing API endpoints for generating social media content using Google's Gemini AI.

## Setup

1. Make sure you have Node.js installed (version 18+ recommended)
2. Install dependencies: `npm install`
3. Create a `.env` file in the root directory with the following content:
   ```
   GOOGLE_API_KEY=your_google_api_key_here
   PORT=3001
   ```
4. Replace `your_google_api_key_here` with your actual Google Gemini API key

## Running the Server

You can run the server in several ways:

### Development Mode

To run only the backend server:
```
npm run server
```

To run both frontend and backend concurrently:
```
npm run dev:all
```

### Production Mode

For production, you'll want to build the frontend and serve it along with the backend:
```
npm run build
```

Then deploy both the built frontend and the server.

## API Endpoints

### POST /api/generate-post

Generates a social media post based on the provided parameters.

**Request Body:**
```json
{
  "platform": "Instagram",
  "tone": "friendly",
  "topic": "spring gardening tips",
  "hashtagCount": 5,
  "imageSize": "4:5",
  "quality": "balanced"
}
```

**Parameters:**
- `platform`: The social media platform (e.g., Instagram, Twitter, LinkedIn)
- `tone`: The tone of the post (e.g., friendly, professional, casual)
- `topic`: The topic of the post
- `hashtagCount`: Number of hashtags to include (0-10)
- `imageSize`: Aspect ratio for the image (e.g., "1:1", "4:5", "16:9")
- `quality`: Generation quality - "fast", "balanced", or "quality"

**Response:**
```json
{
  "caption": "Generated caption text",
  "hashtags": "Generated hashtags",
  "imagePrompt": "Generated image prompt"
}
```

### GET /api/health

Health check endpoint to verify the server is running.

**Response:**
```json
{
  "status": "ok"
}
```

## Security

- The API key is stored in environment variables and not exposed to the client
- CORS is enabled to restrict access to the API
- Basic error handling is implemented to prevent sensitive information leakage

## Models Used

The server uses different Gemini models based on the requested quality:
- `quality`: gemini-2.5-flash-preview-04-17
- `balanced`: gemma-3n-e4b-it
- `fast`: gemini-2.0-flash-lite

## API Changes

This server uses the latest Google GenAI SDK (@google/genai). The API has been updated from the previous version, and the main changes are:

1. Import statement: `import { GoogleGenAI } from '@google/genai'`
2. Initialization: `const ai = new GoogleGenAI({ apiKey })`
3. Content generation: `ai.models.generateContent({ model: modelName, contents: prompt })`
