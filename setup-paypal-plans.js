import fs from 'fs';
import path from 'path';
import { config } from 'dotenv';

// Load environment variables
config();

const PAYPAL_CLIENT_ID = process.env.PAYPAL_CLIENT_ID;
const PAYPAL_CLIENT_SECRET = process.env.PAYPAL_CLIENT_SECRET;
const PAYPAL_BASE_URL = process.env.PAYPAL_BASE_URL || 'https://api-m.sandbox.paypal.com';

if (!PAYPAL_CLIENT_ID || !PAYPAL_CLIENT_SECRET) {
  console.error('Please set PAYPAL_CLIENT_ID and PAYPAL_CLIENT_SECRET in your .env file');
  process.exit(1);
}

// Get PayPal access token
async function getAccessToken() {
  const auth = Buffer.from(`${PAYPAL_CLIENT_ID}:${PAYPAL_CLIENT_SECRET}`).toString('base64');

  const response = await fetch(`${PAYPAL_BASE_URL}/v1/oauth2/token`, {
    method: 'POST',
    headers: {
      'Authorization': `Basic ${auth}`,
      'Accept': 'application/json',
      'Accept-Language': 'en_US',
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    body: 'grant_type=client_credentials'
  });

  if (!response.ok) {
    throw new Error(`Failed to get access token: ${response.statusText}`);
  }

  const data = await response.json();
  return data.access_token;
}

// Create PayPal product
async function createProduct(accessToken) {
  const response = await fetch(`${PAYPAL_BASE_URL}/v1/catalogs/products`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
      'PayPal-Request-Id': `PROD-${Date.now()}`
    },
    body: JSON.stringify({
      name: "SocialSparkGen",
      description: "AI-powered social media content generation service",
      type: "SERVICE",
      category: "SOFTWARE"
    })
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(`Failed to create product: ${JSON.stringify(error)}`);
  }

  const data = await response.json();
  console.log('Created product:', data.id);
  return data.id;
}

// Create subscription plan
async function createPlan(accessToken, productId, planName, description, price, intervalUnit, intervalCount) {
  const response = await fetch(`${PAYPAL_BASE_URL}/v1/billing/plans`, {
    method: 'POST',
    headers: {
      'Accept': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json',
      'PayPal-Request-Id': `PLAN-${Date.now()}-${Math.random()}`
    },
    body: JSON.stringify({
      product_id: productId,
      name: planName,
      description: description,
      billing_cycles: [
        {
          frequency: {
            interval_unit: intervalUnit,
            interval_count: intervalCount
          },
          tenure_type: "REGULAR",
          sequence: 1,
          total_cycles: 0, // Infinite cycles
          pricing_scheme: {
            fixed_price: {
              value: price.toString(),
              currency_code: "USD"
            }
          }
        }
      ],
      payment_preferences: {
        auto_bill_outstanding: false, // Disable automatic billing on failure
        setup_fee_failure_action: "CANCEL", // Cancel subscription on setup failure
        payment_failure_threshold: 1 // Fail immediately, no retries
      }
    })
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(`Failed to create plan ${planName}: ${JSON.stringify(error)}`);
  }

  const data = await response.json();
  console.log(`Created plan ${planName}:`, data.id);
  return data.id;
}

// Update .env file with plan IDs
function updateEnvFile(planIds) {
  const envPath = path.join(process.cwd(), '.env');
  let envContent = fs.readFileSync(envPath, 'utf8');

  // Add plan IDs to .env
  const planIdLines = [
    '',
    '# PayPal Plan IDs (Auto-generated)',
    `PAYPAL_BASIC_MONTHLY_PLAN_ID=${planIds.basicMonthly}`,
    `PAYPAL_BASIC_YEARLY_PLAN_ID=${planIds.basicYearly}`,
    `PAYPAL_PRO_MONTHLY_PLAN_ID=${planIds.proMonthly}`,
    `PAYPAL_PRO_YEARLY_PLAN_ID=${planIds.proYearly}`,
    `PAYPAL_ULTRA_MONTHLY_PLAN_ID=${planIds.ultraMonthly}`,
    `PAYPAL_ULTRA_YEARLY_PLAN_ID=${planIds.ultraYearly}`,
    ''
  ].join('\n');

  // Remove existing plan IDs if they exist
  envContent = envContent.replace(/\n# PayPal Plan IDs.*?\n\n/s, '\n');

  // Add new plan IDs
  envContent += planIdLines;

  fs.writeFileSync(envPath, envContent);
  console.log('Updated .env file with plan IDs');
}

// Main function
async function main() {
  try {
    console.log('Setting up PayPal subscription plans...');

    // Get access token
    console.log('Getting PayPal access token...');
    const accessToken = await getAccessToken();

    // Create product
    console.log('Creating PayPal product...');
    const productId = await createProduct(accessToken);

    // Create subscription plans
    console.log('Creating subscription plans...');

    const planIds = {
      basicMonthly: await createPlan(accessToken, productId, "Basic Monthly", "Basic plan - Monthly billing", 5, "MONTH", 1),
      basicYearly: await createPlan(accessToken, productId, "Basic Yearly", "Basic plan - Yearly billing", 48, "YEAR", 1),
      proMonthly: await createPlan(accessToken, productId, "Pro Monthly", "Pro plan - Monthly billing", 15, "MONTH", 1),
      proYearly: await createPlan(accessToken, productId, "Pro Yearly", "Pro plan - Yearly billing", 144, "YEAR", 1),
      ultraMonthly: await createPlan(accessToken, productId, "Ultra Monthly", "Ultra plan - Monthly billing", 30, "MONTH", 1),
      ultraYearly: await createPlan(accessToken, productId, "Ultra Yearly", "Ultra plan - Yearly billing", 288, "YEAR", 1)
    };

    // Update .env file
    console.log('Updating .env file...');
    updateEnvFile(planIds);

    console.log('✅ PayPal setup completed successfully!');
    console.log('Plan IDs have been added to your .env file.');
    console.log('Restart your server to load the new environment variables.');

  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
  }
}

main();