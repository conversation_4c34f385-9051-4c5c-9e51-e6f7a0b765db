# PayPal Payment Integration Setup

This guide will help you set up PayPal payments for your SocialSparkAI application.

## Prerequisites

1. A PayPal Developer account
2. A PayPal Business account (for live payments)

## Setup Steps

### 1. Create PayPal Developer Account

1. Go to [PayPal Developer](https://developer.paypal.com/)
2. Sign in with your PayPal account or create a new one
3. Navigate to "My Apps & Credentials"

### 2. Create a PayPal Application

1. Click "Create App"
2. Choose "Default Application" for app name
3. Select your business account for live payments
4. Choose "Features" you want to enable:
   - **Checkout** (required for payments)
   - **Vault** (optional, for storing payment methods)

### 3. Get Your Credentials

After creating the app, you'll get:

#### Sandbox Credentials (for testing)
- **Client ID**: `your_sandbox_client_id`
- **Client Secret**: `your_sandbox_client_secret`

#### Live Credentials (for production)
- **Client ID**: `your_live_client_id`
- **Client Secret**: `your_live_client_secret`

### 4. Configure Environment Variables

Create a `.env` file in your project root (copy from `.env.example`):

```bash
# PayPal Configuration
PAYPAL_CLIENT_ID=your_paypal_client_id_here
PAYPAL_CLIENT_SECRET=your_paypal_client_secret_here

# Environment: 'sandbox' for testing, 'live' for production
PAYPAL_ENVIRONMENT=sandbox

# Frontend PayPal Client ID (same as backend client ID)
VITE_PAYPAL_CLIENT_ID=your_paypal_client_id_here
```

### 5. Testing with Sandbox

For testing, use PayPal's sandbox environment:

1. Set `PAYPAL_ENVIRONMENT=sandbox` in your `.env` file
2. Use sandbox credentials
3. Test with PayPal's test accounts:
   - **Buyer Account**: Use the test buyer account provided by PayPal
   - **Seller Account**: Your sandbox business account

#### Test Credit Cards (Sandbox)
- Visa: `****************`
- Mastercard: `****************`
- Amex: `***************`

### 6. Going Live

When ready for production:

1. Set `PAYPAL_ENVIRONMENT=live` in your `.env` file
2. Replace sandbox credentials with live credentials
3. Ensure your PayPal business account is verified
4. Test with small amounts first

## Payment Flow

1. **User selects a plan** on the pricing page
2. **PayPal button appears** if user is logged in
3. **PayPal popup opens** for payment
4. **User completes payment** with PayPal
5. **Backend verifies payment** with PayPal API
6. **User's plan is upgraded** automatically
7. **Confirmation email** (optional, not implemented yet)

## Security Notes

- Never expose your Client Secret in frontend code
- Always verify payments on the backend
- Use webhooks for production to handle payment updates
- Store payment records for accounting

## Supported Plans

The integration supports the following subscription plans:

- **Basic**: $5/month or $4/month (yearly)
- **Pro**: $15/month or $12/month (yearly)
- **Ultra**: $30/month or $25/month (yearly)

## Troubleshooting

### Common Issues

1. **"PayPal Unavailable"**: Check your credentials and environment settings
2. **Payment verification fails**: Ensure backend can reach PayPal API
3. **CORS errors**: Make sure your domain is whitelisted in PayPal settings

### Debug Mode

Add this to your `.env` for more detailed logging:
```bash
DEBUG=paypal*
```

### Testing Checklist

- [ ] Sandbox payments work
- [ ] User plan updates after successful payment
- [ ] Failed payments are handled gracefully  
- [ ] User receives appropriate feedback
- [ ] Payment amounts match plan prices

## Support

For PayPal-specific issues, consult:
- [PayPal Developer Documentation](https://developer.paypal.com/docs/)
- [PayPal Integration Wizard](https://developer.paypal.com/developer/applications/)
- [PayPal Community](https://www.paypal-community.com/)